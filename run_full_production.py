#!/usr/bin/env python3
"""
Execute Full Production Image Generation
Simple script to run the complete 4,783 product processing
"""

import os
import sys
import subprocess

def run_full_production():
    """Execute the full production image generation system"""
    
    print("🚀 Hemp Database - Full Production Image Generation")
    print("=" * 60)
    print("🎯 Target: 4,783 unique products")
    print("💰 Estimated Cost: $14.35")
    print("⏱️  Estimated Time: 4-6 hours")
    print("📊 Processing in batches of 50 products")
    print("")
    
    # Check if Replicate API token is set
    if not os.getenv('REPLICATE_API_TOKEN'):
        print("❌ ERROR: REPLICATE_API_TOKEN not configured")
        print("   Please set your token:")
        print("   export REPLICATE_API_TOKEN='your-token-here'")
        return False
    
    # Check if virtual environment exists
    venv_path = "production_venv/bin/activate"
    if not os.path.exists(venv_path):
        print("❌ ERROR: Virtual environment not found")
        print("   Please create it first:")
        print("   python3 -m venv production_venv")
        print("   source production_venv/bin/activate")
        print("   pip install replicate")
        return False
    
    print("✅ Environment ready - starting production run...")
    print("")
    
    try:
        # Activate virtual environment and run the full production generator
        cmd = f"source {venv_path} && python3 full_mcp_production_generator.py"
        
        print("🔄 Executing:")
        print(f"   {cmd}")
        print("")
        
        # Execute the command
        result = subprocess.run(cmd, shell=True, executable='/bin/bash')
        
        if result.returncode == 0:
            print("✅ Production run completed successfully!")
            return True
        else:
            print(f"❌ Production run failed with code: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Execution error: {e}")
        return False

if __name__ == "__main__":
    
    # Check for command line options
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help":
            print("Usage:")
            print("  python3 run_full_production.py          # Run full production")
            print("  python3 run_full_production.py --help   # Show this help")
            sys.exit(0)
    
    success = run_full_production()
    sys.exit(0 if success else 1)