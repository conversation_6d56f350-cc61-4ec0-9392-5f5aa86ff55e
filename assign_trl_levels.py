#!/usr/bin/env python3
"""
Assign Technology Readiness Levels (TRL) to Products
Maps products to TRL 1-9 scale based on keywords and descriptions
"""

import os
import re
import logging
from typing import Dict, List, Tuple, Optional
from supabase import create_client, Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TRLAssigner:
    def __init__(self):
        self.supabase = self._init_supabase()
        self.trl_mappings = self._define_trl_mappings()
        
    def _init_supabase(self) -> Client:
        """Initialize Supabase client"""
        url = os.getenv('VITE_SUPABASE_URL', 'https://ktoqznqmlnxrtvubewyz.supabase.co')
        key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        if not key:
            key = os.getenv('VITE_SUPABASE_ANON_KEY')
        return create_client(url, key)
    
    def _define_trl_mappings(self) -> Dict[int, Dict]:
        """Define TRL levels and their characteristics"""
        return {
            9: {
                'stage': 'Commercial Deployment',
                'keywords': ['commercial', 'industrial scale', 'mass production', 'market ready', 'certified', 'approved', 'retail', 'available'],
                'description': 'Actual system proven in operational environment'
            },
            8: {
                'stage': 'System Complete',
                'keywords': ['production ready', 'qualified', 'tested', 'validated', 'pilot production', 'demonstration'],
                'description': 'System complete and qualified'
            },
            7: {
                'stage': 'Prototype Demonstration',
                'keywords': ['prototype', 'pilot', 'demonstration', 'field test', 'beta', 'trial'],
                'description': 'System prototype demonstration in operational environment'
            },
            6: {
                'stage': 'Technology Demonstration',
                'keywords': ['demonstration', 'pilot plant', 'test facility', 'validation', 'proof of concept'],
                'description': 'Technology demonstrated in relevant environment'
            },
            5: {
                'stage': 'Technology Development',
                'keywords': ['development', 'testing', 'optimization', 'formulation', 'process development'],
                'description': 'Technology validated in relevant environment'
            },
            4: {
                'stage': 'Research Validation',
                'keywords': ['research', 'laboratory', 'experimental', 'analysis', 'characterization'],
                'description': 'Technology validation in lab'
            },
            3: {
                'stage': 'Proof of Concept',
                'keywords': ['concept', 'feasibility', 'preliminary', 'initial', 'exploratory'],
                'description': 'Analytical and experimental critical function'
            },
            2: {
                'stage': 'Technology Concept',
                'keywords': ['concept', 'theoretical', 'proposed', 'potential', 'possibility'],
                'description': 'Technology concept formulated'
            },
            1: {
                'stage': 'Basic Research',
                'keywords': ['basic research', 'fundamental', 'discovery', 'observation'],
                'description': 'Basic principles observed'
            }
        }
    
    def determine_trl_level(self, product: Dict) -> Tuple[int, str, Dict]:
        """Determine TRL level for a product based on name and description"""
        name = (product.get('name') or '').lower()
        description = (product.get('description') or '').lower()
        combined_text = f"{name} {description}"
        
        # Check for commercial indicators
        commercial_indicators = [
            'available now', 'in stock', 'for sale', 'order now', 'buy now',
            'ships within', 'pricing', 'wholesale', 'retail'
        ]
        
        if any(indicator in combined_text for indicator in commercial_indicators):
            return 9, 'Commercial Deployment', self._get_moq_estimate(product)
        
        # Check each TRL level from highest to lowest
        for trl_level in range(9, 0, -1):
            mapping = self.trl_mappings[trl_level]
            if any(keyword in combined_text for keyword in mapping['keywords']):
                return trl_level, mapping['stage'], self._get_moq_estimate(product)
        
        # Default to TRL 5 if no clear indicators
        return 5, 'Technology Development', self._get_moq_estimate(product)
    
    def _get_moq_estimate(self, product: Dict) -> Dict:
        """Estimate minimum order quantity based on product type and TRL"""
        moq_data = {'moq_quantity': None, 'moq_unit': None, 'lead_time_days': None}
        
        name = (product.get('name') or '').lower()
        description = (product.get('description') or '').lower()
        
        # Extract quantity patterns
        qty_patterns = [
            (r'(\d+)\s*(kg|kilogram)', 'kg'),
            (r'(\d+)\s*(lb|pound)', 'lb'),
            (r'(\d+)\s*(ton|tonne)', 'ton'),
            (r'(\d+)\s*(liter|litre|l)', 'L'),
            (r'(\d+)\s*(gallon|gal)', 'gal'),
            (r'(\d+)\s*(unit|piece|item)', 'units')
        ]
        
        for pattern, unit in qty_patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                moq_data['moq_quantity'] = float(match.group(1))
                moq_data['moq_unit'] = unit
                break
        
        # Default MOQ based on product category
        if not moq_data['moq_quantity']:
            if 'fiber' in name:
                moq_data['moq_quantity'] = 100
                moq_data['moq_unit'] = 'kg'
            elif 'oil' in name:
                moq_data['moq_quantity'] = 5
                moq_data['moq_unit'] = 'L'
            elif 'seed' in name:
                moq_data['moq_quantity'] = 25
                moq_data['moq_unit'] = 'kg'
            elif 'extract' in name or 'isolate' in name:
                moq_data['moq_quantity'] = 1
                moq_data['moq_unit'] = 'kg'
            elif 'panel' in name or 'board' in name:
                moq_data['moq_quantity'] = 100
                moq_data['moq_unit'] = 'units'
        
        # Estimate lead time based on TRL
        return moq_data
    
    def process_products(self):
        """Process all products and assign TRL levels"""
        logger.info("Loading products without TRL assignments...")
        
        # Get products
        products = self.supabase.table('uses_products').select(
            'id, name, description, plant_part_id, industry_sub_category_id'
        ).limit(1000).execute()
        
        # Check existing TRL assignments
        existing_trl = self.supabase.table('product_trl_stages').select('product_id').execute()
        products_with_trl = set(trl['product_id'] for trl in existing_trl.data) if existing_trl.data else set()
        
        products_to_process = [p for p in products.data if p['id'] not in products_with_trl]
        
        logger.info(f"Found {len(products_to_process)} products without TRL assignments")
        
        if not products_to_process:
            logger.info("All products already have TRL assignments")
            return
        
        # Process products
        trl_assignments = []
        trl_distribution = {i: 0 for i in range(1, 10)}
        
        for product in products_to_process:
            trl_level, stage, moq_data = self.determine_trl_level(product)
            
            assignment = {
                'product_id': product['id'],
                'trl_level': trl_level,
                'commercial_stage': stage,
                'moq_quantity': moq_data['moq_quantity'],
                'moq_unit': moq_data['moq_unit'],
                'lead_time_days': self._estimate_lead_time(trl_level),
                'certification_status': 'Certified' if trl_level >= 8 else 'In Progress' if trl_level >= 6 else 'Not Started',
                'scalability_assessment': self._assess_scalability(trl_level)
            }
            
            trl_assignments.append(assignment)
            trl_distribution[trl_level] += 1
            
            if len(trl_assignments) % 100 == 0:
                logger.info(f"Processed {len(trl_assignments)} products...")
        
        # Save assignments in batches
        logger.info("Saving TRL assignments to database...")
        batch_size = 100
        total_saved = 0
        
        for i in range(0, len(trl_assignments), batch_size):
            batch = trl_assignments[i:i+batch_size]
            try:
                self.supabase.table('product_trl_stages').insert(batch).execute()
                total_saved += len(batch)
                logger.info(f"Saved batch {i//batch_size + 1}, total: {total_saved}")
            except Exception as e:
                logger.error(f"Error saving batch: {e}")
        
        # Report results
        logger.info("\n" + "="*60)
        logger.info("TRL ASSIGNMENT COMPLETE")
        logger.info(f"Total products processed: {len(products_to_process)}")
        logger.info(f"Total assignments saved: {total_saved}")
        logger.info("\nTRL Distribution:")
        for trl in range(9, 0, -1):
            if trl_distribution[trl] > 0:
                logger.info(f"  TRL {trl} ({self.trl_mappings[trl]['stage']}): {trl_distribution[trl]} products")
        logger.info("="*60)
    
    def _estimate_lead_time(self, trl_level: int) -> int:
        """Estimate lead time in days based on TRL level"""
        lead_times = {
            9: 7,    # Commercial - 1 week
            8: 14,   # Production ready - 2 weeks
            7: 30,   # Prototype - 1 month
            6: 60,   # Demonstration - 2 months
            5: 90,   # Development - 3 months
            4: 180,  # Research - 6 months
            3: 365,  # Proof of concept - 1 year
            2: 730,  # Concept - 2 years
            1: 1095  # Basic research - 3 years
        }
        return lead_times.get(trl_level, 90)
    
    def _assess_scalability(self, trl_level: int) -> str:
        """Assess scalability based on TRL level"""
        if trl_level >= 8:
            return "Fully Scalable"
        elif trl_level >= 6:
            return "Scalable with Investment"
        elif trl_level >= 4:
            return "Limited Scalability"
        else:
            return "Research Phase"

def main():
    """Main entry point"""
    assigner = TRLAssigner()
    assigner.process_products()

if __name__ == "__main__":
    main()