#!/usr/bin/env python3
"""
Fast Bulk Image Update - Apply as many Replicate URLs as possible quickly
"""
import json
import subprocess
import time

def load_all_generated_images():
    """Load all generated images from results file"""
    results = {}
    
    with open('logs/full_mcp_production_results.jsonl', 'r') as f:
        for line in f:
            if line.strip():
                result = json.loads(line.strip())
                product_id = result['product_id']
                image_url = result['image_url']
                results[product_id] = image_url
    
    return results

def create_large_batch_sql(results, batch_size=100):
    """Create large batch SQL updates"""
    product_items = list(results.items())
    batches = []
    
    # Create batches of batch_size
    for i in range(0, len(product_items), batch_size):
        batch = product_items[i:i + batch_size]
        batches.append(batch)
    
    return batches

def execute_via_supabase_mcp(batch, batch_num, total_batches):
    """Execute batch via MCP Supabase"""
    print(f"🔄 Executing Batch {batch_num}/{total_batches} - {len(batch)} products")
    
    # Build CASE statement
    case_statements = []
    product_ids = []
    
    for product_id, image_url in batch:
        safe_url = image_url.replace("'", "''")
        case_statements.append(f"WHEN {product_id} THEN '{safe_url}'")
        product_ids.append(str(product_id))
    
    # Show first few products being updated
    print(f"   Products: {product_ids[:5]}{'...' if len(product_ids) > 5 else ''}")
    
    # Create SQL for manual execution
    sql = f"""UPDATE uses_products 
SET image_url = CASE id 
    {' '.join(case_statements)}
    ELSE image_url 
END
WHERE id IN ({','.join(product_ids)});"""
    
    # Save to file for MCP execution
    filename = f"batch_update_{batch_num:03d}.sql"
    with open(filename, 'w') as f:
        f.write(f"-- Batch {batch_num}/{total_batches}\n")
        f.write(f"-- Products: {len(batch)}\n\n")
        f.write(sql)
    
    print(f"   📄 Saved: {filename}")
    return True

def main():
    print("🚀 FAST BULK IMAGE UPDATE")
    print("=" * 50)
    
    # Load all results
    results = load_all_generated_images()
    print(f"📁 Loaded {len(results)} generated images")
    
    # Create batches
    batches = create_large_batch_sql(results, batch_size=100)
    print(f"📦 Created {len(batches)} batches of 100 products each")
    
    # Create first 10 batches for immediate execution
    print(f"\n🎯 Creating SQL files for first 10 batches (1,000 products):")
    
    for i, batch in enumerate(batches[:10], 1):
        execute_via_supabase_mcp(batch, i, len(batches))
        time.sleep(0.1)
    
    print(f"\n✅ Created 10 SQL batch files")
    print(f"📊 This will update {min(1000, len(results))} products with generated images")
    print(f"🔧 Execute these files via MCP Supabase to apply updates")
    
    # Show summary
    print(f"\n📈 PROGRESS:")
    print(f"   Total Generated: {len(results)} images")
    print(f"   Ready to Update: 1,000 products")
    print(f"   Remaining: {max(0, len(results) - 1000)} products")
    print(f"   Cost: $14.15 for all {len(results)} professional images")

if __name__ == "__main__":
    main()