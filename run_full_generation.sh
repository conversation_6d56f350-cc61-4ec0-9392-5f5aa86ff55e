#!/bin/bash
"""
Full Production Image Generation - Background Execution
Runs the complete 4,783 product image generation in the background
"""

echo "🚀 Starting Full Hemp Database Image Generation"
echo "=" * 60
echo "🎯 Target: 4,783 products (96 batches)"
echo "💰 Estimated Cost: $14.35"
echo "⏱️  Estimated Time: 4-6 hours"
echo "📁 Logs: logs/full_production_run.log"
echo ""

# Activate virtual environment and run in background
source production_venv/bin/activate

# Run with nohup to continue even if terminal closes
nohup python3 full_production_run.py > logs/full_production_background.log 2>&1 &

# Get the process ID
PID=$!

echo "✅ Production run started successfully!"
echo "📊 Process ID: $PID"
echo ""
echo "📋 To monitor progress:"
echo "   tail -f logs/full_production_run.log"
echo "   tail -f logs/full_production_background.log"
echo ""
echo "📈 To check current progress:"
echo "   cat logs/production_progress.json"
echo ""
echo "⏹️  To stop the process:"
echo "   kill $PID"
echo ""
echo "🎉 The system will process all 4,783 products automatically!"