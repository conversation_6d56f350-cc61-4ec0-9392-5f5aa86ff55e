#!/usr/bin/env python3
"""
Advanced AI Image Generator - Refined for Hemp Database
Generates highly accurate, context-aware product images using intelligent prompt engineering
"""
import os
import sys
import json
import time
import requests
import psycopg2
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv
import logging
import re
import hashlib
from pathlib import Path

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedImageGenerator:
    def __init__(self):
        self.conn = psycopg2.connect(os.getenv('DATABASE_URL'))
        self.cursor = self.conn.cursor()
        
        # Load industry and plant part mappings
        self._load_database_mappings()
        
        # Provider setup
        self.replicate_token = os.getenv('REPLICATE_API_TOKEN')
        self.enabled = bool(self.replicate_token)
        
    def _load_database_mappings(self):
        """Load industry and plant part data for context"""
        try:
            # Load plant parts
            self.cursor.execute("SELECT id, name FROM plant_parts")
            self.plant_parts = {id: name for id, name in self.cursor.fetchall()}
            
            # Load industries  
            self.cursor.execute("SELECT id, name FROM industry_sub_categories")
            self.industries = {id: name for id, name in self.cursor.fetchall()}
            
            logger.info(f"Loaded {len(self.plant_parts)} plant parts, {len(self.industries)} industries")
        except Exception as e:
            logger.error(f"Error loading mappings: {e}")
            self.plant_parts = {}
            self.industries = {}
    
    def analyze_product_context(self, product: Dict) -> Dict:
        """Deep analysis of product for context-aware image generation"""
        name = product.get('name', '').lower()
        description = product.get('description', '').lower()
        plant_part_id = product.get('plant_part_id')
        industry_id = product.get('industry_sub_category_id')
        
        # Get plant part and industry names
        plant_part = self.plant_parts.get(plant_part_id, 'hemp').lower()
        industry = self.industries.get(industry_id, 'general').lower()
        
        # Product type classification
        product_type = self._classify_product_type(name, description)
        
        # Material properties
        material_props = self._extract_material_properties(name, description)
        
        # Form factor analysis
        form_factor = self._determine_form_factor(name, description)
        
        # Application context
        application = self._determine_application_context(name, description, industry)
        
        # Visual style requirements
        visual_style = self._determine_visual_style(product_type, industry)
        
        return {
            'product_type': product_type,
            'plant_part': plant_part,
            'industry': industry,
            'material_props': material_props,
            'form_factor': form_factor,
            'application': application,
            'visual_style': visual_style,
            'name': product.get('name', ''),
            'description_snippet': description[:100]
        }
    
    def _classify_product_type(self, name: str, description: str) -> str:
        """Classify product into specific categories"""
        
        # Electronics & Technology
        if any(word in name + description for word in [
            'battery', 'electrode', 'circuit', 'electronic', 'sensor', 'chip',
            'capacitor', 'conductor', 'semiconductor', 'nano', 'quantum'
        ]):
            return 'electronics'
        
        # Personal Care & Cosmetics
        if any(word in name + description for word in [
            'deodorant', 'shampoo', 'lotion', 'cream', 'serum', 'cosmetic',
            'skincare', 'beauty', 'soap', 'balm', 'moisturizer'
        ]):
            return 'personal_care'
        
        # Food & Nutrition
        if any(word in name + description for word in [
            'protein', 'flour', 'oil', 'seed', 'food', 'nutrition', 'supplement',
            'powder', 'drink', 'beverage', 'snack', 'meal'
        ]):
            return 'food_nutrition'
        
        # Construction & Building
        if any(word in name + description for word in [
            'construction', 'building', 'concrete', 'insulation', 'structural',
            'hempcrete', 'brick', 'panel', 'material', 'block'
        ]):
            return 'construction'
        
        # Textiles & Fabrics
        if any(word in name + description for word in [
            'fabric', 'textile', 'fiber', 'cloth', 'canvas', 'rope', 
            'thread', 'yarn', 'woven', 'leather', 'alternative'
        ]):
            return 'textile'
        
        # Pharmaceutical & Medical
        if any(word in name + description for word in [
            'pharmaceutical', 'medical', 'therapeutic', 'medicine', 'drug',
            'treatment', 'therapy', 'clinical', 'capsule', 'tablet'
        ]):
            return 'pharmaceutical'
        
        # Automotive & Transportation
        if any(word in name + description for word in [
            'automotive', 'car', 'vehicle', 'transportation', 'composite',
            'dashboard', 'interior', 'part', 'component'
        ]):
            return 'automotive'
        
        # Packaging & Paper
        if any(word in name + description for word in [
            'packaging', 'paper', 'cardboard', 'box', 'container',
            'wrap', 'bag', 'bottle', 'jar'
        ]):
            return 'packaging'
        
        # Industrial & Manufacturing
        if any(word in name + description for word in [
            'industrial', 'manufacturing', 'machinery', 'equipment',
            'tool', 'component', 'part', 'filter', 'adhesive'
        ]):
            return 'industrial'
        
        return 'general'
    
    def _extract_material_properties(self, name: str, description: str) -> List[str]:
        """Extract visual material properties"""
        properties = []
        
        text = name + " " + description
        
        # Texture properties
        if 'smooth' in text: properties.append('smooth surface')
        if 'rough' in text: properties.append('rough texture')
        if 'fiber' in text: properties.append('fibrous texture')
        if 'woven' in text: properties.append('woven pattern')
        if 'crystalline' in text: properties.append('crystalline structure')
        
        # Color properties
        if 'natural' in text: properties.append('natural hemp color')
        if 'brown' in text: properties.append('brown tones')
        if 'green' in text: properties.append('green hues')
        if 'white' in text: properties.append('white/cream color')
        
        # Physical properties
        if 'lightweight' in text: properties.append('lightweight appearance')
        if 'dense' in text: properties.append('dense material')
        if 'flexible' in text: properties.append('flexible form')
        if 'rigid' in text: properties.append('rigid structure')
        
        return properties
    
    def _determine_form_factor(self, name: str, description: str) -> str:
        """Determine the physical form of the product"""
        text = name + " " + description
        
        # Liquid products
        if any(word in text for word in ['oil', 'tincture', 'liquid', 'serum', 'lotion']):
            return 'liquid_bottle'
        
        # Powder products
        if any(word in text for word in ['powder', 'flour', 'supplement', 'protein']):
            return 'powder_container'
        
        # Solid products
        if any(word in text for word in ['block', 'brick', 'panel', 'sheet', 'board']):
            return 'solid_block'
        
        # Fabric/textile products
        if any(word in text for word in ['fabric', 'textile', 'cloth', 'canvas']):
            return 'textile_roll'
        
        # Electronic components
        if any(word in text for word in ['circuit', 'chip', 'component', 'electrode']):
            return 'electronic_component'
        
        # Small consumer items
        if any(word in text for word in ['deodorant', 'cream', 'balm', 'soap']):
            return 'consumer_package'
        
        return 'general_product'
    
    def _determine_application_context(self, name: str, description: str, industry: str) -> str:
        """Determine the application context for styling"""
        
        # Medical/pharmaceutical context
        if 'pharmaceutical' in industry or any(word in name + description for word in [
            'medical', 'therapeutic', 'clinical', 'pharmaceutical'
        ]):
            return 'medical_clinical'
        
        # Construction context  
        if 'construction' in industry or any(word in name + description for word in [
            'building', 'construction', 'structural', 'hempcrete'
        ]):
            return 'construction_industrial'
        
        # Consumer/retail context
        if any(word in name + description for word in [
            'consumer', 'retail', 'personal', 'cosmetic', 'food', 'deodorant'
        ]):
            return 'consumer_retail'
        
        # Industrial/B2B context
        if any(word in name + description for word in [
            'industrial', 'manufacturing', 'b2b', 'component', 'machinery'
        ]):
            return 'industrial_b2b'
        
        # Laboratory/research context
        if any(word in name + description for word in [
            'research', 'laboratory', 'experimental', 'prototype', 'testing'
        ]):
            return 'laboratory_research'
        
        return 'general_commercial'
    
    def _determine_visual_style(self, product_type: str, industry: str) -> Dict:
        """Determine visual styling based on product type and industry"""
        
        styles = {
            'electronics': {
                'background': 'clean tech workspace with circuit boards',
                'lighting': 'cool blue LED lighting',
                'props': 'electronic components, microchips',
                'mood': 'high-tech, precision, modern'
            },
            'personal_care': {
                'background': 'clean white bathroom or vanity setup',
                'lighting': 'soft natural lighting',
                'props': 'spa accessories, towels, plants',
                'mood': 'clean, natural, wellness-focused'
            },
            'food_nutrition': {
                'background': 'kitchen counter or natural wooden surface',
                'lighting': 'warm natural lighting',
                'props': 'hemp seeds, measuring spoons, health items',
                'mood': 'healthy, natural, appetizing'
            },
            'construction': {
                'background': 'construction site or industrial workspace',
                'lighting': 'industrial lighting',
                'props': 'building materials, tools, blueprints',
                'mood': 'sturdy, professional, industrial'
            },
            'textile': {
                'background': 'fabric studio or clean surface',
                'lighting': 'natural daylight',
                'props': 'sewing tools, fabric samples',
                'mood': 'artisanal, crafted, sustainable'
            },
            'pharmaceutical': {
                'background': 'sterile medical environment',
                'lighting': 'clean clinical lighting',
                'props': 'medical equipment, lab glassware',
                'mood': 'sterile, professional, trustworthy'
            },
            'automotive': {
                'background': 'automotive workshop or tech center',
                'lighting': 'industrial workshop lighting',
                'props': 'automotive parts, tools',
                'mood': 'precision engineering, performance'
            },
            'general': {
                'background': 'clean neutral background',
                'lighting': 'professional product photography lighting',
                'props': 'minimal, hemp plant elements',
                'mood': 'clean, sustainable, natural'
            }
        }
        
        return styles.get(product_type, styles['general'])
    
    def generate_advanced_prompt(self, product: Dict) -> str:
        """Generate highly specific, context-aware prompts"""
        
        context = self.analyze_product_context(product)
        
        # Base product description
        prompt_parts = [
            f"Professional product photography of {context['name']}"
        ]
        
        # Add specific form factor details
        form_factor_prompts = {
            'liquid_bottle': "in elegant glass bottle with professional label design",
            'powder_container': "in clean white container with hemp leaf branding", 
            'solid_block': "showing material texture and structural properties",
            'textile_roll': "folded fabric showing hemp fiber texture and weave pattern",
            'electronic_component': "on high-tech circuit board background",
            'consumer_package': "in premium retail packaging with modern design"
        }
        
        if context['form_factor'] in form_factor_prompts:
            prompt_parts.append(form_factor_prompts[context['form_factor']])
        
        # Add material properties
        if context['material_props']:
            prompt_parts.append(f"highlighting {', '.join(context['material_props'][:2])}")
        
        # Add visual style context
        style = context['visual_style']
        prompt_parts.append(f"in {style['background']}")
        prompt_parts.append(f"with {style['lighting']}")
        
        # Add industry-specific props if relevant
        if context['product_type'] != 'general':
            prompt_parts.append(f"with {style['props']} visible nearby")
        
        # Add mood and quality specifications
        prompt_parts.append(f"{style['mood']} aesthetic")
        prompt_parts.append("ultra-high quality, 4K resolution, commercial photography")
        prompt_parts.append("sharp focus, professional lighting, color accurate")
        
        # Hemp branding element (subtle)
        prompt_parts.append("subtle hemp leaf logo or natural elements")
        
        # Join and clean up prompt
        prompt = ", ".join(prompt_parts)
        
        # Remove redundant words and optimize length
        prompt = re.sub(r'\b(hemp|cannabis|cbd)\b', 'hemp', prompt, flags=re.IGNORECASE)
        prompt = prompt.replace('  ', ' ')
        
        return prompt[:900]  # Keep under 900 chars for API limits
    
    def generate_image_with_replicate(self, prompt: str, product_id: int) -> Optional[str]:
        """Generate image using Replicate's Stable Diffusion"""
        if not self.enabled:
            logger.warning("Replicate not configured")
            return None
            
        try:
            import replicate
            
            logger.info(f"Generating with prompt: {prompt[:100]}...")
            
            # Use high-quality model
            output = replicate.run(
                "stability-ai/stable-diffusion-xl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
                input={
                    "prompt": prompt,
                    "negative_prompt": "blurry, low quality, distorted, cartoon, anime, watermark, text, logo, signature, amateur photography, poor lighting, oversaturated, marijuana leaves, cannabis buds",
                    "width": 768,
                    "height": 768, 
                    "num_outputs": 1,
                    "guidance_scale": 7.5,
                    "num_inference_steps": 50,
                    "scheduler": "DPMSolverMultistep"
                }
            )
            
            if output and len(output) > 0:
                image_url = output[0]
                logger.info(f"✅ Generated: {image_url}")
                return image_url
                
        except Exception as e:
            logger.error(f"Replicate error: {e}")
            
        return None
    
    def update_product_image(self, product_id: int, image_url: str) -> bool:
        """Update product with new image URL"""
        try:
            # Check if it's a Supabase URL that needs updating
            if 'supabase.co' in image_url and not image_url.startswith('https://'):
                # Already a relative path, keep as is
                pass
            elif image_url.startswith('https://replicate.delivery/'):
                # External Replicate URL, keep as is
                pass
            
            self.cursor.execute("""
                UPDATE uses_products 
                SET image_url = %s, updated_at = NOW()
                WHERE id = %s
            """, (image_url, product_id))
            self.conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error updating product {product_id}: {e}")
            self.conn.rollback()
            return False
    
    def process_products_needing_better_images(self, limit: int = 20) -> Dict:
        """Process products with poor quality images"""
        
        # Get products with fallback images or reported as poor quality
        query = """
            SELECT p.id, p.name, p.description, p.plant_part_id, p.industry_sub_category_id,
                   p.image_url, pp.name as plant_part_name, isc.name as industry_name
            FROM uses_products p
            LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id  
            LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
            WHERE p.image_url = '/images/fallbacks/hemp-generic-neutral.png'
               OR p.image_url LIKE '%placeholder%'
               OR p.image_url IS NULL
            ORDER BY p.created_at DESC
            LIMIT %s
        """
        
        self.cursor.execute(query, (limit,))
        products = []
        
        for row in self.cursor.fetchall():
            products.append({
                'id': row[0],
                'name': row[1], 
                'description': row[2],
                'plant_part_id': row[3],
                'industry_sub_category_id': row[4],
                'current_image_url': row[5],
                'plant_part_name': row[6],
                'industry_name': row[7]
            })
        
        logger.info(f"Found {len(products)} products needing better images")
        
        results = {
            'processed': 0,
            'generated': 0,
            'errors': 0,
            'total_cost': 0.0
        }
        
        for product in products:
            try:
                logger.info(f"\n🎨 Processing: {product['name']}")
                logger.info(f"   Current image: {product['current_image_url']}")
                
                # Generate advanced prompt
                prompt = self.generate_advanced_prompt(product)
                logger.info(f"   Prompt: {prompt[:150]}...")
                
                # Generate image
                image_url = self.generate_image_with_replicate(prompt, product['id'])
                
                if image_url:
                    # Update database
                    if self.update_product_image(product['id'], image_url):
                        results['generated'] += 1
                        results['total_cost'] += 0.002  # Approximate cost
                        logger.info(f"   ✅ Updated with: {image_url}")
                    else:
                        results['errors'] += 1
                        logger.error(f"   ❌ Failed to update database")
                else:
                    results['errors'] += 1
                    logger.error(f"   ❌ Failed to generate image")
                
                results['processed'] += 1
                
                # Rate limiting to avoid API limits
                time.sleep(2)
                
            except Exception as e:
                results['errors'] += 1
                logger.error(f"Error processing product {product['id']}: {e}")
        
        return results
    
    def analyze_current_image_quality(self) -> Dict:
        """Analyze current image quality and generate report"""
        
        # Get image distribution
        self.cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN image_url = '/images/fallbacks/hemp-generic-neutral.png' THEN 1 END) as fallback_images,
                COUNT(CASE WHEN image_url LIKE '%replicate%' THEN 1 END) as replicate_images,
                COUNT(CASE WHEN image_url LIKE '%generated%' THEN 1 END) as generated_images,
                COUNT(CASE WHEN image_url LIKE '%supabase%' AND image_url NOT LIKE '%fallback%' THEN 1 END) as custom_images
            FROM uses_products
        """)
        
        stats = self.cursor.fetchone()
        
        # Get sample of AI-generated images for analysis
        self.cursor.execute("""
            SELECT name, description, image_url
            FROM uses_products 
            WHERE image_url LIKE '%replicate%' OR image_url LIKE '%generated%'
            ORDER BY RANDOM() 
            LIMIT 10
        """)
        
        samples = self.cursor.fetchall()
        
        return {
            'total_products': stats[0],
            'fallback_images': stats[1],
            'replicate_images': stats[2], 
            'generated_images': stats[3],
            'custom_images': stats[4],
            'samples': [{'name': s[0], 'description': s[1][:100], 'image_url': s[2]} for s in samples]
        }
    
    def close(self):
        """Close database connection"""
        self.cursor.close()
        self.conn.close()


def main():
    """Run the advanced image generator"""
    generator = AdvancedImageGenerator()
    
    try:
        # Analyze current state
        logger.info("🔍 Analyzing current image quality...")
        quality_report = generator.analyze_current_image_quality()
        
        logger.info(f"\n📊 IMAGE QUALITY ANALYSIS:")
        logger.info(f"  Total Products: {quality_report['total_products']}")
        logger.info(f"  Fallback Images: {quality_report['fallback_images']}")
        logger.info(f"  AI Generated: {quality_report['replicate_images'] + quality_report['generated_images']}")
        logger.info(f"  Custom Images: {quality_report['custom_images']}")
        
        logger.info(f"\n📝 Sample AI Generated Images:")
        for sample in quality_report['samples'][:3]:
            logger.info(f"  - {sample['name']}: {sample['image_url']}")
        
        if not generator.enabled:
            logger.warning("⚠️  Replicate API not configured - cannot generate images")
            return
            
        # Process products needing better images
        logger.info(f"\n🚀 Processing products needing better images...")
        results = generator.process_products_needing_better_images(limit=10)
        
        logger.info(f"\n✅ GENERATION COMPLETE:")
        logger.info(f"  Processed: {results['processed']} products")
        logger.info(f"  Generated: {results['generated']} new images")
        logger.info(f"  Errors: {results['errors']}")
        logger.info(f"  Estimated Cost: ${results['total_cost']:.3f}")
        
    finally:
        generator.close()


if __name__ == "__main__":
    main()