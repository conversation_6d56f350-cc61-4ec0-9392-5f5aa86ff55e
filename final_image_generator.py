#!/usr/bin/env python3
"""
Final Hemp Database Image Generator
TESTED and WORKING image generation system for hemp products
"""
import os
import time
import logging
import json
from typing import Dict, List, Optional, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/final_image_generation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinalImageGenerator:
    
    def __init__(self):
        self.replicate_token = os.getenv('REPLICATE_API_TOKEN')
        self.enabled = bool(self.replicate_token)
        
        # Generation stats
        self.stats = {
            'processed': 0,
            'generated': 0,
            'updated': 0,
            'errors': 0,
            'skipped': 0,
            'total_cost': 0.0
        }
        
        # Ensure logs directory exists
        os.makedirs('logs', exist_ok=True)
        
        logger.info("🎨 FINAL Hemp Database Image Generation System")
        logger.info(f"🔧 Replicate API: {'✅ Enabled' if self.enabled else '❌ Disabled'}")
    
    def classify_product_type(self, name: str, description: str) -> str:
        """Classify product for targeted image generation"""
        text = (name + " " + description).lower()
        
        # Electronics & Technology
        if any(word in text for word in [
            'semiconductor', 'circuit', 'electronic', 'substrate', 'nanoparticle',
            'microorganisms', 'component', 'electrode', 'sensor', 'battery',
            'capacitor', 'conductor', 'nano', 'quantum'
        ]):
            return 'electronics'
        
        # Medical & Therapeutic  
        if any(word in text for word in [
            'medical', 'therapeutic', 'clinical', 'treatment', 'cellulose',
            'membrane', 'dressing', 'drug', 'pharmaceutical', 'medicine',
            'therapy', 'capsule', 'tablet'
        ]):
            return 'medical'
        
        # Personal Care & Cosmetics
        if any(word in text for word in [
            'deodorant', 'shampoo', 'lotion', 'cream', 'serum', 'cosmetic',
            'skincare', 'beauty', 'soap', 'balm', 'moisturizer'
        ]):
            return 'personal_care'
        
        # Food & Nutrition
        if any(word in text for word in [
            'nutritional', 'nutrition', 'protein', 'flour', 'oil', 'seed',
            'food', 'supplement', 'powder', 'drink', 'beverage', 'meal',
            'metabolism', 'bioavailability', 'antioxidant'
        ]):
            return 'nutrition'
        
        # Construction & Building
        if any(word in text for word in [
            'construction', 'building', 'concrete', 'insulation', 'structural',
            'hempcrete', 'brick', 'panel', 'material', 'block'
        ]):
            return 'construction'
        
        # Textiles & Materials
        if any(word in text for word in [
            'fabric', 'textile', 'fiber', 'cloth', 'canvas', 'rope',
            'thread', 'yarn', 'woven', 'leather', 'alternative'
        ]):
            return 'textile'
        
        # Industrial & Manufacturing
        if any(word in text for word in [
            'composite', 'industrial', 'manufacturing', 'machinery',
            'equipment', 'tool', 'component', 'filter', 'adhesive'
        ]):
            return 'industrial'
        
        # Automotive & Transportation
        if any(word in text for word in [
            'automotive', 'car', 'vehicle', 'transportation', 'dashboard',
            'interior', 'part'
        ]):
            return 'automotive'
        
        return 'general'
    
    def generate_advanced_prompt(self, product: Dict) -> str:
        """Generate context-aware prompt for image generation"""
        name = product['name']
        description = product.get('description', '')
        
        # Analyze product
        product_type = self.classify_product_type(name, description)
        
        # Context-specific prompts based on product type
        if product_type == 'electronics':
            prompt = f"Professional product photography of {name}, high-tech electronic component on circuit board, modern electronics laboratory setting, cool LED lighting, precision engineered, commercial photography, 4K resolution, sharp focus"
        elif product_type == 'personal_care':
            prompt = f"Professional product photography of {name}, elegant skincare product in spa setting, natural lighting, marble surface, wellness aesthetic, premium beauty product, commercial photography, 4K resolution"
        elif product_type == 'nutrition':
            prompt = f"Professional product photography of {name}, health supplement in modern kitchen, warm natural lighting, fresh ingredients nearby, nutritious and healthy, commercial photography, 4K resolution"
        elif product_type == 'construction':
            prompt = f"Professional product photography of {name}, building material in construction setting, bright industrial lighting, professional tools nearby, durable and sustainable, commercial photography, 4K resolution"
        elif product_type == 'textile':
            prompt = f"Professional product photography of {name}, premium fabric in design studio, natural daylight, textile samples, sustainable fashion material, commercial photography, 4K resolution"
        else:
            prompt = f"Professional product photography of {name}, clean studio background, professional lighting, high-quality sustainable hemp-based product, commercial photography, 4K resolution, premium presentation"
        
        return prompt
    
    def generate_image_with_replicate(self, prompt: str) -> Optional[str]:
        """Generate image using Replicate API with working model"""
        if not self.enabled:
            logger.warning("⚠️  Replicate API not configured - skipping generation")
            return None
            
        try:
            import replicate
            
            # Use SDXL Lightning (verified working model)
            output = replicate.run(
                "bytedance/sdxl-lightning-4step:5f24084160c9089501c1b3545d9be3c27883ae2239b6f412990e82d4a6210f8f",
                input={
                    "prompt": prompt,
                    "negative_prompt": "blurry, low quality, distorted, cartoon, anime, watermark, text, amateur photography, poor lighting, oversaturated, marijuana leaves, cannabis buds, unprofessional",
                    "width": 768,
                    "height": 768,
                    "num_outputs": 1,
                    "num_inference_steps": 4,
                    "guidance_scale": 2.5,
                    "scheduler": "K_EULER"  # Valid scheduler for this model
                }
            )
            
            if output and len(output) > 0:
                return output[0]
                
        except ImportError:
            logger.error("❌ Replicate module not installed. Run: pip install replicate")
            return None
        except Exception as e:
            logger.error(f"❌ Replicate error: {e}")
            return None
        
        return None
    
    def run_production_test(self, max_products: int = 2):
        """Run final production test with 2 products"""
        
        logger.info("🚀 FINAL PRODUCTION TEST")
        logger.info("=" * 50)
        logger.info(f"🎯 Testing image generation for {max_products} products")
        
        if not self.enabled:
            logger.error("❌ REPLICATE_API_TOKEN not configured")
            logger.info("Set REPLICATE_API_TOKEN environment variable to enable generation")
            return self.stats
        
        # Test products representing different categories
        test_products = [
            {
                'id': 3001,
                'name': 'Hemp-Based Battery Component',
                'description': 'Advanced cellulose substrate for flexible electronics'
            },
            {
                'id': 3002,
                'name': 'Hemp Seed Face Cream',
                'description': 'Premium skincare moisturizer with hemp seed oil'
            }
        ]
        
        for i, product in enumerate(test_products[:max_products], 1):
            try:
                logger.info(f"\n📦 [{i}/{max_products}] Processing: {product['name']}")
                
                # Generate prompt
                prompt = self.generate_advanced_prompt(product)
                product_type = self.classify_product_type(product['name'], product['description'])
                
                logger.info(f"   🔍 Type: {product_type.title()}")
                logger.info(f"   📝 Prompt: {prompt}")
                
                # Generate image
                logger.info("   🔄 Generating image...")
                start_time = time.time()
                image_url = self.generate_image_with_replicate(prompt)
                generation_time = time.time() - start_time
                
                if image_url:
                    logger.info(f"   ✅ SUCCESS! Generated in {generation_time:.1f}s")
                    logger.info(f"   🖼️  Image URL: {str(image_url)}")
                    
                    self.stats['generated'] += 1
                    self.stats['updated'] += 1
                    self.stats['total_cost'] += 0.003  # SDXL Lightning pricing
                    
                    # Save successful result
                    result_data = {
                        'product_id': product['id'],
                        'product_name': product['name'],
                        'product_type': product_type,
                        'image_url': str(image_url),  # Convert to string
                        'prompt': prompt,
                        'generation_time_seconds': generation_time,
                        'generated_at': time.strftime('%Y-%m-%d %H:%M:%S UTC')
                    }
                    
                    # Save to results file
                    results_file = 'logs/successful_generations.json'
                    with open(results_file, 'a') as f:
                        f.write(json.dumps(result_data, indent=2) + '\n')
                    
                    logger.info(f"   💾 Result saved to {results_file}")
                    
                else:
                    self.stats['errors'] += 1
                    logger.error(f"   ❌ Failed to generate image")
                
                self.stats['processed'] += 1
                
                # Rate limiting between generations
                if i < max_products:
                    logger.info("   ⏳ Waiting 3 seconds...")
                    time.sleep(3)
                
            except Exception as e:
                self.stats['errors'] += 1
                logger.error(f"   ❌ Error processing product: {e}")
        
        # Final summary
        logger.info(f"\n🎉 PRODUCTION TEST COMPLETE!")
        logger.info("=" * 40)
        logger.info(f"📊 FINAL RESULTS:")
        logger.info(f"   Processed: {self.stats['processed']}")
        logger.info(f"   Generated: {self.stats['generated']}")
        logger.info(f"   Errors: {self.stats['errors']}")
        
        if self.stats['generated'] > 0 and self.stats['processed'] > 0:
            success_rate = (self.stats['generated'] / self.stats['processed']) * 100
            avg_cost = self.stats['total_cost'] / self.stats['generated']
            
            logger.info(f"   Success Rate: {success_rate:.1f}%")
            logger.info(f"   Total Cost: ${self.stats['total_cost']:.3f}")
            logger.info(f"   Cost per Image: ${avg_cost:.4f}")
            
            if success_rate >= 50:  # 50%+ success rate is acceptable for production
                logger.info(f"\n🚀 SYSTEM VALIDATED FOR PRODUCTION!")
                logger.info(f"   ✅ Ready to process 4,783 products")
                logger.info(f"   💰 Estimated total cost: ${4783 * avg_cost:.2f}")
                logger.info(f"   ⏱️  Estimated time: ~4-6 hours")
                logger.info(f"   📈 Expected success rate: {success_rate:.0f}%")
                
                # Save production parameters
                production_config = {
                    'model': 'bytedance/sdxl-lightning-4step',
                    'cost_per_image': avg_cost,
                    'success_rate': success_rate,
                    'scheduler': 'K_EULER',
                    'inference_steps': 4,
                    'guidance_scale': 2.5,
                    'image_dimensions': '768x768',
                    'validated_at': time.strftime('%Y-%m-%d %H:%M:%S UTC')
                }
                
                with open('logs/production_config.json', 'w') as f:
                    json.dump(production_config, f, indent=2)
                
                logger.info(f"   ⚙️  Production config saved to logs/production_config.json")
            else:
                logger.warning(f"   ⚠️  Low success rate - check API configuration")
        else:
            logger.error(f"   ❌ No images generated - check Replicate API setup")
        
        return self.stats


if __name__ == "__main__":
    generator = FinalImageGenerator()
    
    # Run production test with 2 products
    results = generator.run_production_test(max_products=2)