#!/usr/bin/env python3
"""
Execute All Image Updates via MCP Supabase
Updates all 4,717 generated images to the database in efficient batches
"""
import json
import logging
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_all_generated_results():
    """Load all unique generated image results"""
    results_by_id = {}
    
    try:
        with open('logs/full_mcp_production_results.jsonl', 'r') as f:
            for line in f:
                if line.strip():
                    result = json.loads(line.strip())
                    product_id = result['product_id']
                    # Keep the most recent result for each product
                    if product_id not in results_by_id:
                        results_by_id[product_id] = result['image_url']
        
        logger.info(f"📁 Loaded {len(results_by_id)} unique products with generated images")
        return results_by_id
        
    except Exception as e:
        logger.error(f"❌ Error loading results: {e}")
        return {}

def create_batch_sql(batch_updates):
    """Create SQL for batch update"""
    if not batch_updates:
        return None
    
    case_statements = []
    product_ids = []
    
    for product_id, image_url in batch_updates:
        # Escape single quotes in URLs
        safe_url = image_url.replace("'", "''")
        case_statements.append(f"WHEN {product_id} THEN '{safe_url}'")
        product_ids.append(str(product_id))
    
    sql = f"""UPDATE uses_products 
SET image_url = CASE id 
    {' '.join(case_statements)}
    ELSE image_url 
END
WHERE id IN ({','.join(product_ids)});"""
    
    return sql

def execute_all_updates():
    """Execute all image updates in batches"""
    
    logger.info("🚀 STARTING COMPREHENSIVE IMAGE UPDATE")
    logger.info("=" * 60)
    
    # Load all results
    results_by_id = load_all_generated_results()
    if not results_by_id:
        logger.error("❌ No results found")
        return
    
    total_products = len(results_by_id)
    batch_size = 50
    batches = []
    current_batch = []
    
    # Create batches
    for product_id, image_url in results_by_id.items():
        current_batch.append((product_id, image_url))
        if len(current_batch) >= batch_size:
            batches.append(current_batch)
            current_batch = []
    
    if current_batch:
        batches.append(current_batch)
    
    logger.info(f"📦 Created {len(batches)} batches of up to {batch_size} updates each")
    
    # Generate SQL files for manual execution
    sql_output_file = 'logs/all_image_updates_mcp.sql'
    with open(sql_output_file, 'w') as f:
        f.write("-- Comprehensive Image Updates for Hemp Database\n")
        f.write(f"-- Generated from {total_products} unique products\n")
        f.write(f"-- Total batches: {len(batches)}\n\n")
        
        for i, batch in enumerate(batches, 1):
            f.write(f"-- Batch {i}/{len(batches)} - {len(batch)} products\n")
            sql = create_batch_sql(batch)
            f.write(sql + "\n\n")
    
    logger.info(f"📄 Created SQL file: {sql_output_file}")
    logger.info(f"   Contains {len(batches)} batch UPDATE statements")
    logger.info(f"   Updates {total_products} products total")
    
    # Show first few batches for immediate execution
    logger.info(f"\n🎯 READY FOR MCP EXECUTION:")
    logger.info(f"   Execute batches via MCP Supabase execute_sql")
    logger.info(f"   Each batch updates {batch_size} products")
    logger.info(f"   Total: {total_products} products with Replicate URLs")
    
    return {
        'total_products': total_products,
        'total_batches': len(batches),
        'batch_size': batch_size,
        'sql_file': sql_output_file
    }

if __name__ == "__main__":
    stats = execute_all_updates()
    
    if stats:
        print(f"\n✅ PREPARATION COMPLETE:")
        print(f"   SQL File: {stats['sql_file']}")
        print(f"   Total Products: {stats['total_products']}")
        print(f"   Total Batches: {stats['total_batches']}")
        print(f"   Next: Execute batches via MCP Supabase")