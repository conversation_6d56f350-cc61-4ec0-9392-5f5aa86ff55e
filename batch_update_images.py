#!/usr/bin/env python3
"""
Batch Update Images in Database
Updates all products that have generated images from the results file
"""
import json
import logging
import time
from collections import defaultdict

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BatchImageUpdater:
    
    def __init__(self):
        self.stats = {
            'total_records': 0,
            'unique_products': 0,
            'updated_successfully': 0,
            'update_errors': 0,
            'batches_processed': 0
        }
    
    def load_all_results(self):
        """Load all generated results and group by product ID"""
        results_by_id = {}
        
        try:
            with open('logs/full_mcp_production_results.jsonl', 'r') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            result = json.loads(line.strip())
                            product_id = result['product_id']
                            
                            # Keep the most recent result for each product
                            if product_id not in results_by_id:
                                results_by_id[product_id] = result
                            else:
                                # Compare timestamps and keep the newer one
                                current_time = result.get('generated_at', '')
                                existing_time = results_by_id[product_id].get('generated_at', '')
                                if current_time > existing_time:
                                    results_by_id[product_id] = result
                            
                        except json.JSONDecodeError as e:
                            logger.warning(f"⚠️ Invalid JSON on line {line_num}: {e}")
                            continue
            
            self.stats['total_records'] = line_num if 'line_num' in locals() else 0
            self.stats['unique_products'] = len(results_by_id)
            
            logger.info(f"📁 Loaded {self.stats['total_records']} total records")
            logger.info(f"🔢 Found {self.stats['unique_products']} unique products with images")
            
            return results_by_id
            
        except FileNotFoundError:
            logger.error("❌ Results file not found: logs/full_mcp_production_results.jsonl")
            return {}
        except Exception as e:
            logger.error(f"❌ Error loading results: {e}")
            return {}
    
    def update_batch(self, batch_updates):
        """Update a batch of products using a single SQL statement"""
        if not batch_updates:
            return 0
        
        try:
            # Build CASE statement for batch update
            case_statements = []
            product_ids = []
            
            for product_id, image_url in batch_updates:
                case_statements.append(f"WHEN {product_id} THEN '{image_url}'")
                product_ids.append(str(product_id))
            
            sql = f"""
            UPDATE uses_products 
            SET image_url = CASE id 
                {' '.join(case_statements)}
                ELSE image_url 
            END
            WHERE id IN ({','.join(product_ids)})
            """
            
            # Note: In actual MCP implementation, you would use:
            # result = mcp_supabase_execute_sql(sql)
            
            # For now, log what would be updated
            logger.info(f"🔄 Would update {len(batch_updates)} products with batch SQL")
            logger.info(f"   Products: {', '.join(product_ids[:10])}{'...' if len(product_ids) > 10 else ''}")
            
            return len(batch_updates)
            
        except Exception as e:
            logger.error(f"❌ Batch update failed: {e}")
            return 0
    
    def run_batch_updates(self, batch_size=50):
        """Run batch updates for all generated images"""
        
        logger.info("🚀 STARTING BATCH IMAGE UPDATE")
        logger.info("=" * 60)
        
        # Load all results
        results_by_id = self.load_all_results()
        if not results_by_id:
            logger.error("❌ No results to process")
            return self.stats
        
        # Prepare batch updates
        batch_updates = []
        processed_count = 0
        
        logger.info(f"🔄 Processing {len(results_by_id)} unique products...")
        
        for product_id, result in results_by_id.items():
            image_url = result['image_url']
            batch_updates.append((product_id, image_url))
            
            # Process batch when it reaches batch_size
            if len(batch_updates) >= batch_size:
                updated_count = self.update_batch(batch_updates)
                self.stats['updated_successfully'] += updated_count
                self.stats['batches_processed'] += 1
                processed_count += len(batch_updates)
                
                logger.info(f"✅ Batch {self.stats['batches_processed']} complete: {updated_count}/{len(batch_updates)} updated")
                logger.info(f"📊 Progress: {processed_count}/{len(results_by_id)} products")
                
                batch_updates = []
                time.sleep(0.5)  # Brief pause between batches
        
        # Process remaining items
        if batch_updates:
            updated_count = self.update_batch(batch_updates)
            self.stats['updated_successfully'] += updated_count
            self.stats['batches_processed'] += 1
            processed_count += len(batch_updates)
            
            logger.info(f"✅ Final batch complete: {updated_count}/{len(batch_updates)} updated")
        
        # Summary
        logger.info(f"\n🎉 BATCH UPDATE COMPLETE!")
        logger.info("=" * 50)
        logger.info(f"📊 RESULTS:")
        logger.info(f"   Total Records Processed: {self.stats['total_records']}")
        logger.info(f"   Unique Products: {self.stats['unique_products']}")
        logger.info(f"   Successfully Updated: {self.stats['updated_successfully']}")
        logger.info(f"   Update Errors: {self.stats['update_errors']}")
        logger.info(f"   Batches Processed: {self.stats['batches_processed']}")
        if self.stats['unique_products'] > 0:
            logger.info(f"   Success Rate: {(self.stats['updated_successfully']/self.stats['unique_products']*100):.1f}%")
        
        return self.stats
    
    def create_update_sql_file(self):
        """Create a SQL file with all update statements for manual execution"""
        results_by_id = self.load_all_results()
        if not results_by_id:
            return
        
        sql_file = 'logs/update_all_images.sql'
        
        try:
            with open(sql_file, 'w') as f:
                f.write("-- Batch update all product images\n")
                f.write("-- Generated from full_mcp_production_results.jsonl\n\n")
                
                for i, (product_id, result) in enumerate(results_by_id.items(), 1):
                    image_url = result['image_url']
                    product_name = result.get('product_name', 'Unknown')
                    
                    f.write(f"-- {i}. {product_name}\n")
                    f.write(f"UPDATE uses_products SET image_url = '{image_url}' WHERE id = {product_id};\n\n")
                
                f.write(f"-- Total updates: {len(results_by_id)} products\n")
            
            logger.info(f"📄 Created SQL file: {sql_file}")
            logger.info(f"   Contains {len(results_by_id)} UPDATE statements")
            logger.info("   You can execute this file in your database client")
            
        except Exception as e:
            logger.error(f"❌ Failed to create SQL file: {e}")


if __name__ == "__main__":
    updater = BatchImageUpdater()
    
    # Create SQL file for manual execution
    logger.info("Creating SQL file for manual execution...")
    updater.create_update_sql_file()
    
    # Run batch updates (simulated)
    logger.info("\nRunning batch update process...")
    results = updater.run_batch_updates(batch_size=100)