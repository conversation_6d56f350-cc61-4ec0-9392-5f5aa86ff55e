#!/usr/bin/env python3
"""
Codebase Reorganization Script
Safely reorganizes the project structure without breaking functionality
"""
import os
import shutil
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CodebaseReorganizer:
    def __init__(self):
        self.root = Path.cwd()
        self.dirs_to_create = [
            "src",
            "src/database",
            "src/agents", 
            "src/scrapers",
            "src/ai_providers",
            "src/utils",
            "scripts",
            "scripts/migrations",
            "scripts/fixes",
            "scripts/analysis",
            "tests",
            "tests/test_agents",
            "tests/test_scrapers",
            "tests/test_database",
            "docs",
            "archive",
            "archive/session_summaries",
            "archive/old_scripts"
        ]
        
    def create_directory_structure(self):
        """Create the new directory structure"""
        logger.info("Creating directory structure...")
        
        for dir_path in self.dirs_to_create:
            path = self.root / dir_path
            path.mkdir(parents=True, exist_ok=True)
            
            # Add __init__.py to Python packages
            if dir_path.startswith("src") or dir_path.startswith("tests"):
                init_file = path / "__init__.py"
                if not init_file.exists():
                    init_file.touch()
                    
        logger.info("✅ Directory structure created")
    
    def categorize_files(self):
        """Categorize existing files for migration"""
        categories = {
            'agents': [],
            'scrapers': [],
            'database': [],
            'ai_providers': [],
            'tests': [],
            'docs': [],
            'scripts': [],
            'archive': []
        }
        
        # Pattern matching for categorization
        for file in self.root.glob("*.py"):
            name = file.name.lower()
            
            if 'test_' in name or '_test' in name:
                categories['tests'].append(file)
            elif 'agent' in name or 'launch_' in name:
                categories['agents'].append(file)
            elif 'scraper' in name or 'scrape' in name:
                categories['scrapers'].append(file)
            elif 'db_' in name or 'database' in name or 'supabase' in name:
                categories['database'].append(file)
            elif 'ai_provider' in name or 'multi_provider' in name:
                categories['ai_providers'].append(file)
            elif any(x in name for x in ['fix_', 'cleanup_', 'merge_', 'check_']):
                categories['scripts'].append(file)
            elif 'SESSION_' in name.upper() or 'REPORT_' in name.upper():
                categories['archive'].append(file)
        
        # Markdown files
        for file in self.root.glob("*.md"):
            name = file.name.upper()
            if 'SESSION' in name or 'REPORT' in name or 'SUMMARY' in name:
                categories['archive'].append(file)
            elif name in ['README.MD', 'CLAUDE.MD', 'GEMINI.MD']:
                categories['docs'].append(file)
        
        return categories
    
    def generate_migration_report(self):
        """Generate a report of what will be moved where"""
        categories = self.categorize_files()
        
        report = ["# File Migration Report\n"]
        report.append(f"Total Python files: {len(list(self.root.glob('*.py')))}\n")
        report.append(f"Total Markdown files: {len(list(self.root.glob('*.md')))}\n")
        
        for category, files in categories.items():
            if files:
                report.append(f"\n## {category.title()} ({len(files)} files)")
                for file in sorted(files)[:10]:  # Show first 10
                    report.append(f"  - {file.name}")
                if len(files) > 10:
                    report.append(f"  ... and {len(files) - 10} more")
        
        # Files that will remain in root
        all_categorized = set()
        for files in categories.values():
            all_categorized.update(files)
        
        remaining = []
        for file in self.root.glob("*"):
            if file.is_file() and file not in all_categorized:
                if not file.name.startswith('.') and file.suffix in ['.py', '.md', '.txt']:
                    remaining.append(file)
        
        if remaining:
            report.append(f"\n## Remaining in Root ({len(remaining)} files)")
            for file in sorted(remaining)[:20]:
                report.append(f"  - {file.name}")
        
        return "\n".join(report)
    
    def create_consolidated_requirements(self):
        """Merge all requirements files into one"""
        logger.info("Consolidating requirements files...")
        
        all_requirements = set()
        
        # Read all requirements files
        for req_file in self.root.glob("requirements*.txt"):
            with open(req_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        all_requirements.add(line)
        
        # Write consolidated file
        with open(self.root / "requirements_consolidated.txt", 'w') as f:
            f.write("# Consolidated requirements file\n")
            f.write("# Generated by reorganize_codebase.py\n\n")
            for req in sorted(all_requirements):
                f.write(f"{req}\n")
        
        logger.info(f"✅ Created requirements_consolidated.txt with {len(all_requirements)} packages")
    
    def safe_migrate_file(self, source: Path, dest_dir: Path, new_name: str = None):
        """Safely copy a file to new location (doesn't delete original)"""
        dest_path = dest_dir / (new_name or source.name)
        
        if dest_path.exists():
            logger.warning(f"Skipping {source.name} - already exists at destination")
            return False
        
        try:
            shutil.copy2(source, dest_path)
            logger.info(f"✅ Copied {source.name} → {dest_path.relative_to(self.root)}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to copy {source.name}: {e}")
            return False


def main():
    reorganizer = CodebaseReorganizer()
    
    # Step 1: Create directory structure
    reorganizer.create_directory_structure()
    
    # Step 2: Generate migration report
    report = reorganizer.generate_migration_report()
    
    with open("MIGRATION_REPORT.md", 'w') as f:
        f.write(report)
    
    logger.info("\n📄 Migration report saved to MIGRATION_REPORT.md")
    
    # Step 3: Consolidate requirements
    reorganizer.create_consolidated_requirements()
    
    # Step 4: Copy AI providers (safe to do now)
    logger.info("\n🚀 Starting safe migration of ai_providers...")
    
    ai_provider_files = reorganizer.root.glob("ai_providers/*.py")
    for file in ai_provider_files:
        reorganizer.safe_migrate_file(
            file, 
            reorganizer.root / "src" / "ai_providers",
            file.name
        )
    
    logger.info("\n✅ Phase 1 complete!")
    logger.info("Review MIGRATION_REPORT.md before proceeding with full migration")
    logger.info("\nNext steps:")
    logger.info("1. Review the migration report")
    logger.info("2. Test that automation still works")
    logger.info("3. Run with --migrate flag to perform actual migration")


if __name__ == "__main__":
    main()