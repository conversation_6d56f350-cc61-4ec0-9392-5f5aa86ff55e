#!/usr/bin/env python3
"""
Phase 2 Migration - Move more files while keeping automation running
"""
import os
import shutil
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def migrate_scrapers():
    """Move scraper files to src/scrapers/"""
    root = Path.cwd()
    dest_dir = root / "src" / "scrapers"
    
    scraper_files = [
        "enhanced_company_scraper.py",
        "enhanced_research_scraper.py",
        "hemp_industry_daily_scraper.py",
        "simple_logo_scraper.py",
        "run_simple_scrapers.py",
        "run_image_scrapers_fixed.py"
    ]
    
    moved = 0
    for file in scraper_files:
        if (root / file).exists():
            dest = dest_dir / file
            if not dest.exists():
                shutil.copy2(root / file, dest)
                logger.info(f"  ✅ Moved {file}")
                moved += 1
    
    return moved


def migrate_database_modules():
    """Move database modules to src/database/"""
    root = Path.cwd()
    dest_dir = root / "src" / "database"
    
    db_files = [
        "db_manager.py",
        "export_database.py",
        "populate_supabase_db.py",
        "monitor_database_health.py"
    ]
    
    moved = 0
    for file in db_files:
        if (root / file).exists():
            dest = dest_dir / file
            if not dest.exists():
                shutil.copy2(root / file, dest)
                logger.info(f"  ✅ Moved {file}")
                moved += 1
    
    return moved


def migrate_utils():
    """Move utility files to src/utils/"""
    root = Path.cwd()
    dest_dir = root / "src" / "utils"
    
    util_files = [
        "improved_ai_parser.py",
        "quick_fix_automation.py",
        "monitor_automation.py",
        "simple_agent_dashboard.py"
    ]
    
    moved = 0
    for file in util_files:
        if (root / file).exists():
            dest = dest_dir / file
            if not dest.exists():
                shutil.copy2(root / file, dest)
                logger.info(f"  ✅ Moved {file}")
                moved += 1
    
    return moved


def create_readme():
    """Create README for new structure"""
    readme_content = """# Hemp Database Project

## New Structure

```
src/
├── orchestrator.py      # Main entry point
├── agents/             # AI agents for various tasks
├── database/           # Database operations
├── scrapers/           # Web scraping modules
├── ai_providers/       # AI provider integrations
└── utils/              # Utility functions

scripts/
├── fixes/              # One-off fix scripts
├── analysis/           # Analysis and reporting
└── migrations/         # Database migrations

tests/                  # All test files
docs/                   # Documentation
archive/                # Old files and reports
```

## Usage

### Main Orchestrator
```bash
# Run full automation cycle
python src/orchestrator.py cycle

# Run specific tasks
python src/orchestrator.py discover --count 10
python src/orchestrator.py match --batch 50
python src/orchestrator.py images --batch 100

# View statistics
python src/orchestrator.py stats

# Run continuously
python src/orchestrator.py cycle --continuous
```

### Legacy Scripts (Still Working)
- `./START_SCALED_AUTOMATION.sh` - Start the scaled automation
- `python working_product_adder.py` - Add products directly
- `python monitor_automation.py` - Monitor automation status

## Migration Status
- ✅ Phase 1: Directory structure created
- ✅ Phase 2: Core modules organized
- 🔄 Phase 3: Testing and validation
- ⏳ Phase 4: Update all imports
- ⏳ Phase 5: Clean up root directory
"""
    
    with open("README_NEW_STRUCTURE.md", "w") as f:
        f.write(readme_content)
    
    logger.info("✅ Created README_NEW_STRUCTURE.md")


def main():
    logger.info("Starting Phase 2 Migration...")
    
    # Move scrapers
    logger.info("\n🔄 Moving scrapers...")
    scrapers_moved = migrate_scrapers()
    
    # Move database modules  
    logger.info("\n🔄 Moving database modules...")
    db_moved = migrate_database_modules()
    
    # Move utilities
    logger.info("\n🔄 Moving utilities...")
    utils_moved = migrate_utils()
    
    # Create documentation
    create_readme()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("✅ PHASE 2 MIGRATION COMPLETE")
    logger.info(f"  Scrapers moved: {scrapers_moved}")
    logger.info(f"  Database modules moved: {db_moved}")
    logger.info(f"  Utilities moved: {utils_moved}")
    logger.info("\nThe automation is still running normally!")
    logger.info("Check README_NEW_STRUCTURE.md for usage instructions")


if __name__ == "__main__":
    main()