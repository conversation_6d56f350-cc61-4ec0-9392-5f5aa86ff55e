#!/usr/bin/env python3
"""
Continue Migration - Steps 1-5
Move remaining files and update imports
"""
import os
import shutil
from pathlib import Path
import re
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MigrationContinuation:
    def __init__(self):
        self.root = Path.cwd()
        self.moved_files = []
        self.updated_imports = []
        
    def step1_move_remaining_files(self):
        """Step 1: Move remaining categorized files"""
        logger.info("STEP 1: Moving remaining files...")
        
        # Move remaining agent files
        agent_files = [
            "launch_enhanced_ai_agent.py",
            "launch_fiber_agent.py", 
            "launch_leaves_agent.py",
            "launch_roots_agent.py",
            "launch_cannabinoids_agent.py",
            "launch_all_agents.py",
            "run_agent_with_images.py",
            "simple_deepseek_agent.py"
        ]
        
        old_agents_dir = self.root / "archive" / "old_agents"
        old_agents_dir.mkdir(exist_ok=True)
        
        for file in agent_files:
            if (self.root / file).exists():
                dest = old_agents_dir / file
                if not dest.exists():
                    shutil.move(str(self.root / file), str(dest))
                    self.moved_files.append(file)
                    logger.info(f"  ✅ Archived {file}")
        
        # Move demo/test files that aren't in tests/ yet
        demo_files = [
            "demo_ai_providers.py",
            "demo_deduplication.py",
            "quick_test_setup.py"
        ]
        
        for file in demo_files:
            if (self.root / file).exists():
                dest = self.root / "scripts" / "demos" / file
                dest.parent.mkdir(exist_ok=True)
                if not dest.exists():
                    shutil.move(str(self.root / file), str(dest))
                    self.moved_files.append(file)
                    logger.info(f"  ✅ Moved {file} to scripts/demos/")
        
        return len(self.moved_files)
    
    def step2_update_imports_in_src(self):
        """Step 2: Update imports in moved files"""
        logger.info("\nSTEP 2: Updating imports in src/ files...")
        
        # Update imports in src directory
        for py_file in self.root.glob("src/**/*.py"):
            self._update_file_imports(py_file)
        
        return len(self.updated_imports)
    
    def _update_file_imports(self, file_path: Path):
        """Update imports in a single file"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            original_content = content
            
            # Update imports for moved modules
            replacements = [
                # AI providers
                (r'from ai_providers\.multi_provider', 'from src.ai_providers.multi_provider'),
                (r'import ai_providers\.multi_provider', 'import src.ai_providers.multi_provider'),
                
                # Database
                (r'from db_manager', 'from src.database.db_manager'),
                (r'import db_manager', 'import src.database.db_manager'),
                
                # If file references working_product_adder from src/
                (r'from working_product_adder', 'from ..working_product_adder'),
            ]
            
            for old_pattern, new_pattern in replacements:
                content = re.sub(old_pattern, new_pattern, content)
            
            if content != original_content:
                with open(file_path, 'w') as f:
                    f.write(content)
                self.updated_imports.append(file_path.name)
                logger.info(f"  ✅ Updated imports in {file_path.name}")
                
        except Exception as e:
            logger.error(f"  ❌ Error updating {file_path.name}: {e}")
    
    def step3_test_new_structure(self):
        """Step 3: Test the new structure"""
        logger.info("\nSTEP 3: Testing new structure...")
        
        tests = [
            ("Orchestrator stats", "python src/orchestrator.py stats"),
            ("AI providers", "python -c 'from src.ai_providers.multi_provider import MultiProviderAI; print(\"✅ AI providers import works\")'"),
            ("Database connection", "python -c 'from src.database.connection import get_db_connection; print(\"✅ Database import works\")'")
        ]
        
        passed = 0
        for test_name, command in tests:
            result = os.system(f"source venv_dedup/bin/activate && {command} > /dev/null 2>&1")
            if result == 0:
                logger.info(f"  ✅ {test_name} - PASSED")
                passed += 1
            else:
                logger.info(f"  ❌ {test_name} - FAILED")
        
        return passed, len(tests)
    
    def step4_archive_duplicates(self):
        """Step 4: Archive duplicate and old files"""
        logger.info("\nSTEP 4: Archiving duplicate/old files...")
        
        # Patterns for files to archive
        archive_patterns = [
            "*_old.py",
            "*_backup.py", 
            "*_orig.py",
            "test_*.py",  # Any remaining test files
            "old_*.py"
        ]
        
        archived_dir = self.root / "archive" / "old_scripts"
        archived_dir.mkdir(parents=True, exist_ok=True)
        
        archived_count = 0
        for pattern in archive_patterns:
            for file in self.root.glob(pattern):
                if file.is_file():
                    dest = archived_dir / file.name
                    if not dest.exists():
                        shutil.move(str(file), str(dest))
                        archived_count += 1
                        logger.info(f"  ✅ Archived {file.name}")
        
        return archived_count
    
    def step5_update_documentation(self):
        """Step 5: Update main README and documentation"""
        logger.info("\nSTEP 5: Updating documentation...")
        
        # Create new main README
        readme_content = """# Industrial Hemp Database

A comprehensive database of industrial hemp products with AI-powered discovery and automation.

## 🚀 Quick Start

```bash
# View database statistics
python src/orchestrator.py stats

# Add new products
python src/orchestrator.py discover --count 10

# Run full automation cycle
python src/orchestrator.py cycle

# Run continuous automation
./START_SCALED_AUTOMATION.sh
```

## 📁 Project Structure

```
.
├── src/                    # Core application code
│   ├── orchestrator.py     # Main entry point
│   ├── agents/            # AI-powered agents
│   ├── database/          # Database operations
│   ├── scrapers/          # Web scraping modules
│   └── ai_providers/      # AI integrations
├── scripts/               # Utility scripts
├── tests/                 # Test files
├── docs/                  # Documentation
└── logs/                  # Application logs
```

## 📊 Current Status

- **Products**: 650+ and growing
- **Companies**: 141
- **Automation**: Adding 50+ products daily
- **Image Coverage**: 100%

## 🔧 Configuration

Create a `.env` file with:

```
DATABASE_URL=postgresql://...
DEEPSEEK_API_KEY=...
GEMINI_API_KEY=...
```

## 📚 Documentation

- [Architecture Overview](docs/ARCHITECTURE.md)
- [API Documentation](docs/API.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## 🤝 Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

---

*Automated system actively growing the database 24/7*
"""
        
        with open(self.root / "README.md", 'w') as f:
            f.write(readme_content)
        
        logger.info("  ✅ Updated README.md")
        
        # Create architecture documentation
        arch_content = """# Architecture Overview

## System Design

The Hemp Database uses a modular architecture with clear separation of concerns:

### Core Components

1. **Orchestrator** (`src/orchestrator.py`)
   - Central command interface
   - Coordinates all operations
   - Provides CLI interface

2. **Agents** (`src/agents/`)
   - `product_discovery.py` - AI-powered product generation
   - `company_matcher.py` - Matches products to companies
   - `image_manager.py` - Assigns images to products

3. **Database** (`src/database/`)
   - PostgreSQL via Supabase
   - Connection management
   - Query operations

4. **AI Providers** (`src/ai_providers/`)
   - Multi-provider support (DeepSeek, Gemini, OpenAI)
   - Automatic fallback
   - Response validation

## Data Flow

```
User Command → Orchestrator → Agent → AI Provider → Database
                           ↓
                    Validation & Processing
```

## Automation

The system runs continuous automation cycles:
- Product discovery every 30 minutes
- Company matching every 2 hours
- Database cleanup daily

See [scaled_automation.py](../scaled_automation.py) for implementation.
"""
        
        docs_dir = self.root / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        with open(docs_dir / "ARCHITECTURE.md", 'w') as f:
            f.write(arch_content)
        
        logger.info("  ✅ Created ARCHITECTURE.md")
        
        return 2  # Files created
    
    def generate_final_report(self):
        """Generate final migration report"""
        logger.info("\n" + "="*60)
        logger.info("MIGRATION COMPLETE - FINAL REPORT")
        logger.info("="*60)
        
        # Count remaining files in root
        py_files = len(list(self.root.glob("*.py")))
        md_files = len(list(self.root.glob("*.md")))
        
        logger.info(f"\nRoot Directory Status:")
        logger.info(f"  Python files: {py_files} (was 141)")
        logger.info(f"  Markdown files: {md_files} (was 82)")
        
        logger.info(f"\nMigration Summary:")
        logger.info(f"  Files moved: {len(self.moved_files)}")
        logger.info(f"  Imports updated: {len(self.updated_imports)}")
        
        logger.info(f"\nNew Structure:")
        for dir_path in ["src", "scripts", "tests", "docs", "archive"]:
            path = self.root / dir_path
            if path.exists():
                file_count = len(list(path.rglob("*.py")))
                logger.info(f"  {dir_path}/: {file_count} Python files")


def main():
    migrator = MigrationContinuation()
    
    # Step 1: Move remaining files
    moved = migrator.step1_move_remaining_files()
    logger.info(f"  Total moved: {moved} files")
    
    # Step 2: Update imports
    updated = migrator.step2_update_imports_in_src()
    logger.info(f"  Total updated: {updated} files")
    
    # Step 3: Test new structure
    passed, total = migrator.step3_test_new_structure()
    logger.info(f"  Tests: {passed}/{total} passed")
    
    # Step 4: Archive duplicates
    archived = migrator.step4_archive_duplicates()
    logger.info(f"  Archived: {archived} files")
    
    # Step 5: Update documentation
    docs_created = migrator.step5_update_documentation()
    logger.info(f"  Documentation files: {docs_created}")
    
    # Final report
    migrator.generate_final_report()


if __name__ == "__main__":
    main()