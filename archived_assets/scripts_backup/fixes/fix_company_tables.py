#!/usr/bin/env python3
"""
Fix company tables - consolidate hemp_companies into companies
"""
import os
import psycopg2
from dotenv import load_dotenv
import logging

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_company_tables():
    """Sync hemp_companies to companies table"""
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    try:
        # First, insert missing companies from hemp_companies to companies
        cursor.execute("""
            INSERT INTO companies (id, name, description, website, created_at, updated_at)
            SELECT hc.id, hc.name, hc.description, hc.website, hc.created_at, hc.updated_at
            FROM hemp_companies hc
            LEFT JOIN companies c ON hc.id = c.id
            WHERE c.id IS NULL
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                description = EXCLUDED.description,
                website = EXCLUDED.website,
                updated_at = EXCLUDED.updated_at
        """)
        
        inserted = cursor.rowcount
        logger.info(f"Inserted/updated {inserted} companies")
        
        # Verify counts match
        cursor.execute("SELECT COUNT(*) FROM companies")
        companies_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM hemp_companies")
        hemp_count = cursor.fetchone()[0]
        
        logger.info(f"Companies table now has {companies_count} rows")
        logger.info(f"Hemp_companies has {hemp_count} rows")
        
        if companies_count >= hemp_count:
            logger.info("✅ Tables are synchronized")
            conn.commit()
        else:
            logger.error("Tables don't match, rolling back")
            conn.rollback()
            
    except Exception as e:
        logger.error(f"Error: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    fix_company_tables()