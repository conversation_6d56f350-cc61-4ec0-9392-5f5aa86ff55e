#!/usr/bin/env python3
"""
Safe cleanup of low-quality products with proper FK handling
"""

import os
import re
from datetime import datetime
from dotenv import load_dotenv
import psycopg2
from psycopg2.extras import RealDictCursor
import json

load_dotenv()

def connect_db():
    """Connect to the database"""
    return psycopg2.connect(os.environ['DATABASE_URL'])

def get_dependent_tables(conn):
    """Get all tables that reference uses_products"""
    cur = conn.cursor()
    cur.execute("""
        SELECT 
            tc.table_name, 
            kcu.column_name
        FROM 
            information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
          AND ccu.table_name='uses_products'
          AND ccu.column_name='id';
    """)
    
    dependent_tables = cur.fetchall()
    cur.close()
    return dependent_tables

def safe_remove_products(product_ids):
    """Safely remove products with all dependencies"""
    if not product_ids:
        print("No products to remove")
        return
    
    conn = connect_db()
    
    try:
        # Get dependent tables
        print("\n🔍 Checking dependent tables...")
        dependent_tables = get_dependent_tables(conn)
        
        # Delete from dependent tables first (each in its own transaction)
        for table_name, column_name in dependent_tables:
            cur = conn.cursor()
            try:
                cur.execute(f"""
                    DELETE FROM {table_name}
                    WHERE {column_name} = ANY(%s)
                """, (product_ids,))
                
                count = cur.rowcount
                conn.commit()
                if count > 0:
                    print(f"   ✅ Removed {count} records from {table_name}")
            except Exception as e:
                conn.rollback()
                print(f"   ⚠️  Skipped {table_name}: {str(e).split('DETAIL')[0].strip()}")
            finally:
                cur.close()
        
        # Now delete the products
        cur = conn.cursor()
        try:
            cur.execute("""
                DELETE FROM uses_products
                WHERE id = ANY(%s)
                RETURNING id, name
            """, (product_ids,))
            
            deleted_products = cur.fetchall()
            conn.commit()
            
            print(f"\n✅ Successfully removed {len(deleted_products)} products:")
            for prod in deleted_products[:10]:
                print(f"   - {prod[1]}")
            if len(deleted_products) > 10:
                print(f"   ... and {len(deleted_products) - 10} more")
        except Exception as e:
            conn.rollback()
            print(f"\n❌ Could not delete products: {e}")
        finally:
            cur.close()
        
    except Exception as e:
        conn.rollback()
        print(f"\n❌ Error during cleanup: {e}")
    finally:
        conn.close()

def main():
    """Main function"""
    # Load the cleanup report
    try:
        with open('cleanup_report.json', 'r') as f:
            report = json.load(f)
    except FileNotFoundError:
        print("❌ cleanup_report.json not found. Run cleanup_low_quality_products.py first")
        return
    
    product_ids = [p['id'] for p in report['products']]
    
    print("="*60)
    print("🧹 SAFE PRODUCT CLEANUP")
    print("="*60)
    print(f"\nFound {len(product_ids)} products to remove from cleanup report")
    
    # Show summary
    print("\nProduct types to remove:")
    print(f"   - Generic AI Products: {report['summary']['ai_products']}")
    print(f"   - Variant Products: {report['summary']['variants']}")
    print(f"   - Empty Descriptions: {report['summary']['empty_descriptions']}")
    print(f"   - Other Duplicates: {report['summary']['other']}")
    
    # Execute cleanup
    print("\n" + "="*60)
    safe_remove_products(product_ids)

if __name__ == "__main__":
    main()