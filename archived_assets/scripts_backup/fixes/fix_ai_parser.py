#!/usr/bin/env python3
"""
Fix the AI response parser to handle various formats
"""
import re
import json
from typing import List, Dict

def parse_ai_product_response(response_text: str) -> List[Dict]:
    """
    Parse AI response containing product information in various formats
    """
    products = []
    
    # First try to find JSON arrays
    json_matches = re.findall(r'\[[\s\S]*?\]', response_text)
    for match in json_matches:
        try:
            parsed = json.loads(match)
            if isinstance(parsed, list) and len(parsed) > 0:
                # Check if it looks like product data
                if isinstance(parsed[0], dict) and any(key in parsed[0] for key in ['name', 'description', 'product']):
                    return parsed
        except:
            pass
    
    # Split by numbered items
    # Pattern: 1. **Product Name** or 1. Product Name
    sections = re.split(r'^\d+\.\s*', response_text, flags=re.MULTILINE)[1:]
    
    for section in sections:
        if not section.strip():
            continue
            
        # Extract product name (might be bold or not)
        name_match = re.match(r'\*?\*?([^*\n]+?)\*?\*?(?:\s*[:\-]|$)', section)
        if not name_match:
            continue
            
        product = {
            'name': name_match.group(1).strip().rstrip(':'),
            'description': '',
            'benefits': [],
            'technical_specs': '',
            'applications': []
        }
            
            # Parse description
            desc_match = re.search(r'[Dd]escription[:\s]*([^-•\n]+)', section)
            if desc_match:
                product['description'] = desc_match.group(1).strip()
            else:
                # Try to get first substantial text after product name
                desc_match = re.search(r'\*\*[:\s]*([^-•\n]+)', section)
                if desc_match:
                    product['description'] = desc_match.group(1).strip()
            
            # Parse benefits (look for bullet points or numbered items)
            benefits_section = re.search(r'[Bb]enefits?[:\s]*\n([\s\S]*?)(?=[Tt]echnical|$)', section)
            if benefits_section:
                benefit_lines = re.findall(r'[-•]\s*([^\n]+)', benefits_section.group(1))
                product['benefits'] = [b.strip() for b in benefit_lines[:3]]
            
            # Parse technical specs
            specs_match = re.search(r'[Tt]echnical [Ss]pecs?[:\s]*\n([\s\S]*?)(?=[Aa]pplications|$|\n\n)', section)
            if specs_match:
                specs_text = specs_match.group(1).strip()
                # Join multiple spec lines
                spec_lines = [line.strip() for line in specs_text.split('\n') if line.strip() and line.strip().startswith('-')]
                product['technical_specs'] = ' | '.join(spec_lines) if spec_lines else specs_text
            
            # Ensure we have minimum required fields
            if product['name'] and len(product['description']) > 20:
                products.append(product)
    
    # If no products found, try a simpler pattern
    if not products:
        # Look for any numbered items
        simple_pattern = r'^\d+\.\s*([^\n]+)'
        simple_matches = re.findall(simple_pattern, response_text, re.MULTILINE)
        
        for match in simple_matches:
            # Clean up the match
            name = match.strip()
            # Remove any trailing punctuation or description
            if ':' in name:
                name = name.split(':')[0]
            
            if len(name) > 3 and len(name) < 100:
                products.append({
                    'name': name,
                    'description': f"Innovative hemp product {name}",
                    'benefits': ["Sustainable", "High-performance", "Eco-friendly"],
                    'technical_specs': "Advanced hemp-based technology"
                })
    
    return products

# Test the parser
if __name__ == "__main__":
    test_response = """Here are three innovative hemp fiber products:  

1. **HempFlex Insulation Panels**  
   - **Description**: Lightweight, eco-friendly insulation panels made from compressed hemp fibers, designed for residential and commercial buildings to improve thermal and acoustic performance.  
   - **Benefits**:  
     - Superior thermal regulation, reducing energy costs by up to 30%.  
     - Naturally fire-resistant and mold-resistant due to hemp's inherent properties.  
     - Carbon-negative production, sequestering CO2 during growth and processing.  
   - **Technical Specs**:  
     - Density: 40-60 kg/m³  
     - R-Value: 3.5-4.0 per inch  

2. **HempWeave Automotive Interior Fabric**  
   - **Description**: A durable, sustainable textile made from woven hemp fibers, tailored for car seats, dashboards, and door panels as a premium alternative to synthetic materials.  
   - **Benefits**:  
     - High tensile strength (50% stronger than cotton), ensuring longevity.  
     - Breathable and hypoallergenic, enhancing passenger comfort.  
     - Biodegradable and recyclable, reducing automotive waste."""
    
    products = parse_ai_product_response(test_response)
    print(f"Parsed {len(products)} products:")
    for p in products:
        print(f"\n- {p['name']}")
        print(f"  Description: {p['description'][:80]}...")
        print(f"  Benefits: {len(p['benefits'])} items")
        print(f"  Has specs: {'Yes' if p.get('technical_specs') else 'No'}")