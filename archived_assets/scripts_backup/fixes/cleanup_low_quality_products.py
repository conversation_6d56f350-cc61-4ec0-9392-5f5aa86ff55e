#!/usr/bin/env python3
"""
Clean up low-quality and duplicate products from the database
"""

import os
import re
from datetime import datetime
from dotenv import load_dotenv
import psycopg2
from psycopg2.extras import RealDictCursor
import json

load_dotenv()

def connect_db():
    """Connect to the database"""
    return psycopg2.connect(os.environ['DATABASE_URL'])

def find_products_to_remove():
    """Find low-quality products that should be removed"""
    conn = connect_db()
    cur = conn.cursor(cursor_factory=RealDictCursor)
    
    products_to_remove = []
    
    # 1. Find generic AI products
    print("\n🔍 Finding generic AI products...")
    cur.execute("""
        SELECT id, name, description, source_agent
        FROM uses_products
        WHERE name SIMILAR TO 'AI Product%|Test Product%|Hemp Product %[0-9]+|Product %[0-9]+'
        ORDER BY id
    """)
    generic_products = cur.fetchall()
    products_to_remove.extend([p['id'] for p in generic_products])
    print(f"   Found {len(generic_products)} generic products")
    
    # 2. Find empty or very short descriptions
    print("\n🔍 Finding products with empty descriptions...")
    cur.execute("""
        SELECT id, name, description
        FROM uses_products
        WHERE description IS NULL 
           OR LENGTH(TRIM(description)) < 10
           OR description = ''
        ORDER BY id
    """)
    empty_desc = cur.fetchall()
    empty_desc_ids = [p['id'] for p in empty_desc if p['id'] not in products_to_remove]
    products_to_remove.extend(empty_desc_ids)
    print(f"   Found {len(empty_desc)} products with empty/short descriptions")
    
    # 3. Find exact duplicates (same name, keep oldest)
    print("\n🔍 Finding exact name duplicates...")
    cur.execute("""
        WITH duplicates AS (
            SELECT LOWER(TRIM(name)) as normalized_name, 
                   COUNT(*) as count,
                   MIN(id) as keep_id
            FROM uses_products
            WHERE name IS NOT NULL
            GROUP BY LOWER(TRIM(name))
            HAVING COUNT(*) > 1
        )
        SELECT p.id, p.name, p.created_at
        FROM uses_products p
        JOIN duplicates d ON LOWER(TRIM(p.name)) = d.normalized_name
        WHERE p.id != d.keep_id
        ORDER BY p.name, p.id
    """)
    exact_dupes = cur.fetchall()
    exact_dupe_ids = [p['id'] for p in exact_dupes if p['id'] not in products_to_remove]
    products_to_remove.extend(exact_dupe_ids)
    print(f"   Found {len(exact_dupes)} exact duplicates to remove")
    
    # 4. Find variant products
    print("\n🔍 Finding variant products...")
    cur.execute("""
        SELECT id, name, description
        FROM uses_products
        WHERE name ~ '\\(Variant [0-9]+\\)$'
        ORDER BY name
    """)
    variants = cur.fetchall()
    
    # For variants, check if base product exists
    variant_ids_to_remove = []
    for variant in variants:
        base_name = re.sub(r'\s*\(Variant \d+\)$', '', variant['name']).strip()
        
        # Check if base product exists
        cur.execute("""
            SELECT id FROM uses_products 
            WHERE LOWER(TRIM(name)) = LOWER(%s) 
            AND id != %s
            LIMIT 1
        """, (base_name, variant['id']))
        
        if cur.fetchone():
            variant_ids_to_remove.append(variant['id'])
    
    products_to_remove.extend([id for id in variant_ids_to_remove if id not in products_to_remove])
    print(f"   Found {len(variant_ids_to_remove)} variants to remove (base products exist)")
    
    # 5. Find plural duplicates
    print("\n🔍 Finding plural duplicates...")
    cur.execute("""
        WITH potential_plurals AS (
            SELECT p1.id as plural_id, 
                   p1.name as plural_name,
                   p2.id as singular_id,
                   p2.name as singular_name
            FROM uses_products p1
            JOIN uses_products p2 
              ON LOWER(TRIM(p1.name)) = LOWER(TRIM(p2.name)) || 's'
              OR LOWER(TRIM(p1.name)) = LOWER(TRIM(p2.name)) || 'es'
            WHERE p1.id != p2.id
        )
        SELECT DISTINCT plural_id, plural_name, singular_id, singular_name
        FROM potential_plurals
        ORDER BY plural_name
    """)
    plural_dupes = cur.fetchall()
    plural_ids = [p['plural_id'] for p in plural_dupes if p['plural_id'] not in products_to_remove]
    products_to_remove.extend(plural_ids)
    print(f"   Found {len(plural_dupes)} plural duplicates")
    
    # Remove duplicates from our list
    products_to_remove = list(set(products_to_remove))
    
    # Get full details for products to remove
    if products_to_remove:
        cur.execute("""
            SELECT id, name, description, created_at, source_agent
            FROM uses_products
            WHERE id = ANY(%s)
            ORDER BY name
        """, (products_to_remove,))
        
        removal_details = cur.fetchall()
    else:
        removal_details = []
    
    cur.close()
    conn.close()
    
    return removal_details

def remove_products(product_ids, dry_run=True):
    """Remove products from database"""
    if not product_ids:
        print("\n✅ No products to remove!")
        return
    
    conn = connect_db()
    cur = conn.cursor()
    
    if dry_run:
        print(f"\n🔍 DRY RUN: Would remove {len(product_ids)} products")
    else:
        print(f"\n🗑️  Removing {len(product_ids)} products...")
        
        try:
            # First delete related records from dependent tables
            dependent_tables = [
                ('ai_generation_costs', 'product_id'),
                ('content_queue', 'product_id'),
                ('product_images', 'product_id'),
                ('product_variants', 'base_product_id'),
                ('product_relationships', 'product_id'),
                ('product_relationships', 'related_product_id')
            ]
            
            for table, column in dependent_tables:
                try:
                    cur.execute(f"""
                        DELETE FROM {table}
                        WHERE {column} = ANY(%s)
                    """, (product_ids,))
                    if cur.rowcount > 0:
                        print(f"   Cleaned {cur.rowcount} records from {table}")
                except Exception as e:
                    # Table might not exist, that's ok
                    conn.rollback()  # Reset transaction state
                    cur = conn.cursor()  # Get new cursor
            
            # Delete products
            cur.execute("""
                DELETE FROM uses_products
                WHERE id = ANY(%s)
                RETURNING id, name
            """, (product_ids,))
            
            deleted = cur.fetchall()
            conn.commit()
            
            print(f"✅ Successfully removed {len(deleted)} products")
            
        except Exception as e:
            conn.rollback()
            print(f"❌ Error removing products: {e}")
    
    cur.close()
    conn.close()

def main():
    """Main cleanup process"""
    print("="*60)
    print("🧹 PRODUCT CLEANUP TOOL")
    print("="*60)
    
    # Find products to remove
    products_to_remove = find_products_to_remove()
    
    if not products_to_remove:
        print("\n✅ No low-quality products found!")
        return
    
    # Summary
    print(f"\n📊 SUMMARY: Found {len(products_to_remove)} products to remove")
    print("-"*60)
    
    # Group by type
    ai_products = [p for p in products_to_remove if p['name'].startswith('AI Product')]
    variants = [p for p in products_to_remove if '(Variant' in p['name']]
    empty_desc = [p for p in products_to_remove if not p['description'] or len(p['description'].strip()) < 10]
    
    print(f"   - Generic AI Products: {len(ai_products)}")
    print(f"   - Variant Products: {len(variants)}")
    print(f"   - Empty/Short Descriptions: {len(empty_desc)}")
    print(f"   - Other Duplicates: {len(products_to_remove) - len(ai_products) - len(variants) - len(empty_desc)}")
    
    # Show sample
    print("\n📋 Sample products to remove:")
    for p in products_to_remove[:10]:
        desc_preview = (p['description'][:30] + '...') if p['description'] else 'No description'
        print(f"   ID {p['id']}: '{p['name']}' - {desc_preview}")
    
    if len(products_to_remove) > 10:
        print(f"   ... and {len(products_to_remove) - 10} more")
    
    # Save report
    report = {
        'cleanup_date': datetime.now().isoformat(),
        'total_to_remove': len(products_to_remove),
        'products': [dict(p) for p in products_to_remove],
        'summary': {
            'ai_products': len(ai_products),
            'variants': len(variants),
            'empty_descriptions': len(empty_desc),
            'other': len(products_to_remove) - len(ai_products) - len(variants) - len(empty_desc)
        }
    }
    
    with open('cleanup_report.json', 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print("\n📄 Detailed report saved to: cleanup_report.json")
    
    # Check for command line argument
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == '--yes':
        print("\n🗑️  Auto-removing products...")
        remove_products([p['id'] for p in products_to_remove], dry_run=False)
    else:
        # Ask for confirmation
        print("\n" + "="*60)
        try:
            response = input("Do you want to remove these products? (yes/no/dry-run): ").lower()
            
            if response == 'yes':
                remove_products([p['id'] for p in products_to_remove], dry_run=False)
            elif response == 'dry-run':
                remove_products([p['id'] for p in products_to_remove], dry_run=True)
            else:
                print("❌ Cleanup cancelled")
        except EOFError:
            print("\n❌ No input provided. Run with --yes to auto-remove")

if __name__ == "__main__":
    main()