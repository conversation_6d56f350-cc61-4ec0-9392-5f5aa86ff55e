#!/usr/bin/env python3
"""
Analyze product quality and identify duplicates/low-quality entries
"""

import os
import re
from datetime import datetime
from dotenv import load_dotenv
import psycopg2
from collections import defaultdict
import json

load_dotenv()

def connect_db():
    """Connect to the database"""
    return psycopg2.connect(os.environ['DATABASE_URL'])

def analyze_products():
    """Analyze products for quality issues"""
    conn = connect_db()
    cur = conn.cursor()
    
    # Get all products
    cur.execute("""
        SELECT id, name, description, created_at, source_agent
        FROM uses_products
        ORDER BY name
    """)
    
    products = cur.fetchall()
    
    # Categories of issues
    issues = {
        'generic_names': [],
        'variants': [],
        'plurals': [],
        'similar_names': defaultdict(list),
        'short_descriptions': [],
        'missing_descriptions': [],
        'test_products': []
    }
    
    # Patterns to detect
    generic_patterns = [
        r'^AI Product',
        r'^Test Product',
        r'^Hemp Product',
        r'^Product \d+',
        r'^Untitled',
        r'^New Product'
    ]
    
    variant_pattern = r'(.+?)\s*\(Variant \d+\)'
    
    # Build name lookup for similarity detection
    name_lookup = {}
    for prod in products:
        name_lower = prod[1].lower().strip()
        name_lookup[prod[0]] = name_lower
    
    print(f"\n📊 ANALYZING {len(products)} PRODUCTS")
    print("="*60)
    
    for product in products:
        id, name, description, created_at, source_agent = product
        is_ai = bool(source_agent)  # If source_agent is not null, it's AI-generated
        name = name.strip() if name else ""
        description = description.strip() if description else ""
        
        # Check for generic/test names
        for pattern in generic_patterns:
            if re.match(pattern, name, re.IGNORECASE):
                issues['generic_names'].append({
                    'id': id,
                    'name': name,
                    'created': created_at.isoformat() if created_at else None,
                    'ai_generated': is_ai
                })
                break
        
        # Check for variant names
        variant_match = re.match(variant_pattern, name)
        if variant_match:
            base_name = variant_match.group(1).strip()
            issues['variants'].append({
                'id': id,
                'name': name,
                'base_name': base_name,
                'ai_generated': is_ai
            })
        
        # Check for plural duplicates
        if name.endswith('s'):
            singular = name[:-1]
            for other_id, other_name in name_lookup.items():
                if other_id != id and other_name == singular.lower():
                    issues['plurals'].append({
                        'id': id,
                        'name': name,
                        'singular_id': other_id,
                        'singular_name': singular
                    })
        
        # Check for similar names (edit distance)
        name_words = set(name.lower().split())
        for other_id, other_name in name_lookup.items():
            if other_id >= id:  # Avoid duplicate comparisons
                continue
            other_words = set(other_name.split())
            
            # Check word overlap
            overlap = len(name_words & other_words)
            if overlap >= 2 and len(name_words) >= 2:
                similarity = overlap / max(len(name_words), len(other_words))
                if similarity >= 0.8:
                    issues['similar_names'][name].append({
                        'id': other_id,
                        'name': other_name,
                        'similarity': similarity
                    })
        
        # Check description quality
        if not description:
            issues['missing_descriptions'].append({
                'id': id,
                'name': name
            })
        elif len(description) < 20:
            issues['short_descriptions'].append({
                'id': id,
                'name': name,
                'description': description,
                'length': len(description)
            })
    
    # Print analysis results
    print("\n🔍 QUALITY ISSUES FOUND:")
    print("-"*60)
    
    print(f"\n1. GENERIC/TEST NAMES ({len(issues['generic_names'])} found):")
    for item in issues['generic_names'][:10]:
        print(f"   ID {item['id']}: '{item['name']}' (AI: {item['ai_generated']})")
    if len(issues['generic_names']) > 10:
        print(f"   ... and {len(issues['generic_names']) - 10} more")
    
    print(f"\n2. VARIANT NAMES ({len(issues['variants'])} found):")
    variant_groups = defaultdict(list)
    for item in issues['variants']:
        variant_groups[item['base_name']].append(item)
    
    for base_name, variants in list(variant_groups.items())[:5]:
        print(f"   Base: '{base_name}'")
        for v in variants[:3]:
            print(f"      ID {v['id']}: '{v['name']}'")
    
    print(f"\n3. PLURAL DUPLICATES ({len(issues['plurals'])} found):")
    for item in issues['plurals'][:10]:
        print(f"   ID {item['id']}: '{item['name']}' (singular: ID {item['singular_id']} '{item['singular_name']}')")
    
    print(f"\n4. SIMILAR NAMES ({len(issues['similar_names'])} groups found):")
    for name, similar in list(issues['similar_names'].items())[:5]:
        print(f"   '{name}':")
        for s in similar[:3]:
            print(f"      ID {s['id']}: '{s['name']}' (similarity: {s['similarity']:.1%})")
    
    print(f"\n5. MISSING DESCRIPTIONS ({len(issues['missing_descriptions'])} found):")
    for item in issues['missing_descriptions'][:10]:
        print(f"   ID {item['id']}: '{item['name']}'")
    
    print(f"\n6. SHORT DESCRIPTIONS ({len(issues['short_descriptions'])} found):")
    for item in issues['short_descriptions'][:10]:
        print(f"   ID {item['id']}: '{item['name']}' - '{item['description']}' ({item['length']} chars)")
    
    # Save detailed report
    report = {
        'analysis_date': datetime.now().isoformat(),
        'total_products': len(products),
        'issues': {
            'generic_names': issues['generic_names'],
            'variants': [v for variants in variant_groups.values() for v in variants],
            'plurals': issues['plurals'],
            'similar_names': [
                {'name': name, 'similar': similar} 
                for name, similar in issues['similar_names'].items()
            ],
            'missing_descriptions': issues['missing_descriptions'],
            'short_descriptions': issues['short_descriptions']
        },
        'summary': {
            'generic_count': len(issues['generic_names']),
            'variant_count': len(issues['variants']),
            'plural_count': len(issues['plurals']),
            'similar_groups': len(issues['similar_names']),
            'missing_desc_count': len(issues['missing_descriptions']),
            'short_desc_count': len(issues['short_descriptions'])
        }
    }
    
    with open('product_quality_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print("\n📄 Detailed report saved to: product_quality_report.json")
    
    # Recommend actions
    print("\n🎯 RECOMMENDED ACTIONS:")
    print("-"*60)
    
    total_issues = (len(issues['generic_names']) + len(issues['variants']) + 
                   len(issues['plurals']) + len(issues['missing_descriptions']))
    
    print(f"1. Remove {len(issues['generic_names'])} generic/test products")
    print(f"2. Merge {len(issues['variants'])} variant products")
    print(f"3. Remove {len(issues['plurals'])} plural duplicates")
    print(f"4. Update {len(issues['missing_descriptions'])} products with missing descriptions")
    print(f"\nTotal products to clean up: ~{total_issues}")
    
    cur.close()
    conn.close()
    
    return report

if __name__ == "__main__":
    analyze_products()