#!/usr/bin/env python3
"""Debug what DeepSeek is returning"""

import asyncio
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set API key
os.environ['DEEPSEEK_API_KEY'] = '***********************************'

from utils.ai_providers import DeepSeekProvider


async def test_json_response():
    """Test DeepSeek JSON responses"""
    
    print("Creating DeepSeek provider...")
    deepseek = DeepSeekProvider()
    
    # Test 1: Basic prompt without JSON instruction
    print("\n=== Test 1: Basic prompt ===")
    prompt1 = """List 2 hemp plastic products with descriptions."""
    
    try:
        response = await deepseek.generate(prompt1)
        print(f"Response type: {type(response)}")
        print(f"Response: {response}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: With JSON format request
    print("\n\n=== Test 2: With JSON format ===")
    prompt2 = """List 2 hemp plastic products. Return as JSON array with objects containing 'name' and 'description' fields.
    
IMPORTANT: Return ONLY valid JSON with no additional text or explanation."""
    
    try:
        response = await deepseek.generate(prompt2, response_format="json")
        print(f"Response type: {type(response)}")
        print(f"Response: {response}")
        print(f"\nTrying to parse as JSON...")
        import json
        parsed = json.loads(response)
        print(f"Parsed successfully: {parsed}")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Structured prompt for product
    print("\n\n=== Test 3: Structured product prompt ===")
    prompt3 = """Extract and structure hemp product information from this data:
        
Source: Hemp Industry Daily
Title: New Hemp Bioplastic for Packaging
Content: Company XYZ launches hemp-based plastic alternative for food packaging

Return a JSON object with:
{
    "name": "product name",
    "description": "detailed description",
    "plant_part": "seeds|fiber|oil|flower|hurds|roots|leaves|biomass",
    "industry": "main industry category",
    "companies": ["list", "of", "companies"]
}

IMPORTANT: Return ONLY valid JSON with no additional text or explanation."""
    
    try:
        response = await deepseek.generate(prompt3, response_format="json")
        print(f"Raw response: '{response}'")
        print(f"Response length: {len(response)}")
        print(f"First 100 chars: '{response[:100]}'")
        
        # Check if response is empty
        if not response or response.strip() == "":
            print("ERROR: Response is empty!")
        else:
            print("\nTrying to parse...")
            import json
            parsed = json.loads(response)
            print(f"Parsed successfully: {parsed}")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_json_response())