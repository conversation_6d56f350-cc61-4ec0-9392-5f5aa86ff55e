#!/usr/bin/env python3
"""
Check the AI-generated products that were just added
"""

import asyncio
import asyncpg
from urllib.parse import urlparse, unquote
from dotenv import load_dotenv
import os

load_dotenv()


async def check_recent_ai_products():
    """Check recently added AI products"""
    
    database_url = os.getenv('DATABASE_URL')
    parsed = urlparse(database_url)
    
    conn = await asyncpg.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=unquote(parsed.password),
        ssl='require',
        statement_cache_size=0
    )
    
    print("\n" + "="*70)
    print("🤖 RECENT AI-GENERATED PRODUCTS")
    print("="*70)
    
    # Get last 10 AI-generated products
    query = """
        SELECT p.id, p.name, p.description, pp.name as plant_part, 
               p.created_at, p.source_agent
        FROM uses_products p
        JOIN plant_parts pp ON p.plant_part_id = pp.id
        WHERE p.source_agent = 'ai_discovery_agent'
        ORDER BY p.created_at DESC
        LIMIT 10
    """
    
    rows = await conn.fetch(query)
    
    print(f"\nFound {len(rows)} recent AI-generated products:\n")
    
    for row in rows:
        print(f"ID: {row['id']}")
        print(f"Plant Part: {row['plant_part']}")
        print(f"Name: {row['name']}")
        print(f"Description: {row['description'][:100]}..." if row['description'] else "No description")
        print(f"Created: {row['created_at']}")
        print("-" * 50)
    
    # Get plant part distribution
    print("\n📊 AI PRODUCTS BY PLANT PART:")
    print("-" * 50)
    
    query = """
        SELECT pp.name, COUNT(*) as count
        FROM uses_products p
        JOIN plant_parts pp ON p.plant_part_id = pp.id
        WHERE p.source_agent = 'ai_discovery_agent'
        GROUP BY pp.name
        ORDER BY count DESC
    """
    
    rows = await conn.fetch(query)
    
    for row in rows:
        print(f"{row['name']}: {row['count']} products")
    
    # Overall stats
    total_ai = await conn.fetchval(
        "SELECT COUNT(*) FROM uses_products WHERE source_agent = 'ai_discovery_agent'"
    )
    
    total_all = await conn.fetchval("SELECT COUNT(*) FROM uses_products")
    
    print(f"\n📈 OVERALL STATS:")
    print(f"Total AI-generated products: {total_ai}")
    print(f"Total products in database: {total_all}")
    print(f"AI-generated percentage: {total_ai/total_all*100:.1f}%")
    
    await conn.close()


if __name__ == "__main__":
    asyncio.run(check_recent_ai_products())