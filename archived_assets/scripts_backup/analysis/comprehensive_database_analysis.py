#!/usr/bin/env python3
"""
Comprehensive analysis of the Industrial Hemp Database
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timedelta
from collections import defaultdict
import json
from dotenv import load_dotenv

load_dotenv()

def connect_db():
    """Connect to the database"""
    return psycopg2.connect(os.environ['DATABASE_URL'])

def analyze_database():
    """Perform comprehensive database analysis"""
    conn = connect_db()
    cur = conn.cursor(cursor_factory=RealDictCursor)
    
    analysis = {
        'timestamp': datetime.now().isoformat(),
        'summary': {},
        'products': {},
        'companies': {},
        'research': {},
        'quality_metrics': {},
        'growth_metrics': {},
        'coverage_gaps': {},
        'ai_impact': {}
    }
    
    print("🔍 ANALYZING INDUSTRIAL HEMP DATABASE")
    print("="*60)
    
    # 1. Summary Statistics
    print("\n📊 Summary Statistics...")
    cur.execute("SELECT COUNT(*) as count FROM uses_products")
    analysis['summary']['total_products'] = cur.fetchone()['count']
    
    cur.execute("SELECT COUNT(*) as count FROM hemp_companies")
    analysis['summary']['total_companies'] = cur.fetchone()['count']
    
    cur.execute("SELECT COUNT(*) as count FROM research_entries")
    analysis['summary']['total_research'] = cur.fetchone()['count']
    
    cur.execute("SELECT COUNT(DISTINCT plant_part_id) as count FROM uses_products")
    analysis['summary']['plant_parts_covered'] = cur.fetchone()['count']
    
    cur.execute("SELECT COUNT(DISTINCT industry_sub_category_id) as count FROM uses_products WHERE industry_sub_category_id IS NOT NULL")
    analysis['summary']['industries_covered'] = cur.fetchone()['count']
    
    # 2. Products Analysis
    print("\n📦 Products Analysis...")
    
    # Products by plant part
    cur.execute("""
        SELECT pp.name as plant_part, COUNT(p.id) as count
        FROM plant_parts pp
        LEFT JOIN uses_products p ON p.plant_part_id = pp.id
        GROUP BY pp.id, pp.name
        ORDER BY count DESC
    """)
    analysis['products']['by_plant_part'] = [dict(row) for row in cur.fetchall()]
    
    # Products by industry
    cur.execute("""
        SELECT i.name as industry, COUNT(p.id) as count
        FROM industries i
        LEFT JOIN industry_sub_categories isc ON isc.industry_id = i.id
        LEFT JOIN uses_products p ON p.industry_sub_category_id = isc.id
        GROUP BY i.id, i.name
        ORDER BY count DESC
    """)
    analysis['products']['by_industry'] = [dict(row) for row in cur.fetchall()]
    
    # Product completeness
    cur.execute("""
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN description IS NOT NULL AND LENGTH(description) > 50 THEN 1 END) as with_description,
            COUNT(CASE WHEN benefits_advantages IS NOT NULL AND array_length(benefits_advantages, 1) > 0 THEN 1 END) as with_benefits,
            COUNT(CASE WHEN technical_specifications IS NOT NULL THEN 1 END) as with_tech_specs,
            COUNT(CASE WHEN image_url IS NOT NULL THEN 1 END) as with_images,
            COUNT(CASE WHEN primary_company_id IS NOT NULL THEN 1 END) as with_company,
            COUNT(CASE WHEN source_agent IS NOT NULL THEN 1 END) as ai_generated
        FROM uses_products
    """)
    completeness = cur.fetchone()
    analysis['products']['completeness'] = dict(completeness)
    
    # AI vs Manual products
    cur.execute("""
        SELECT 
            source_agent,
            COUNT(*) as count,
            AVG(LENGTH(COALESCE(description, ''))) as avg_desc_length
        FROM uses_products
        GROUP BY source_agent
    """)
    analysis['products']['by_source'] = [dict(row) for row in cur.fetchall()]
    
    # 3. Quality Metrics
    print("\n✨ Quality Metrics...")
    
    # Description quality
    cur.execute("""
        SELECT 
            CASE 
                WHEN description IS NULL OR LENGTH(description) = 0 THEN '0. No description'
                WHEN LENGTH(description) < 50 THEN '1. Very short (< 50 chars)'
                WHEN LENGTH(description) < 100 THEN '2. Short (50-100 chars)'
                WHEN LENGTH(description) < 200 THEN '3. Medium (100-200 chars)'
                ELSE '4. Good (200+ chars)'
            END as quality,
            COUNT(*) as count
        FROM uses_products
        GROUP BY quality
        ORDER BY quality
    """)
    analysis['quality_metrics']['description_quality'] = [dict(row) for row in cur.fetchall()]
    
    # Data completeness score distribution
    cur.execute("""
        SELECT 
            CASE 
                WHEN data_completeness_score IS NULL THEN 'Not scored'
                WHEN data_completeness_score < 0.3 THEN 'Low (< 30%)'
                WHEN data_completeness_score < 0.5 THEN 'Medium (30-50%)'
                WHEN data_completeness_score < 0.7 THEN 'Good (50-70%)'
                ELSE 'Excellent (70%+)'
            END as score_range,
            COUNT(*) as count
        FROM uses_products
        GROUP BY score_range
        ORDER BY score_range
    """)
    analysis['quality_metrics']['completeness_scores'] = [dict(row) for row in cur.fetchall()]
    
    # 4. Growth Metrics
    print("\n📈 Growth Metrics...")
    
    # Daily growth
    cur.execute("""
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as products_added,
            COUNT(CASE WHEN source_agent IS NOT NULL THEN 1 END) as ai_products
        FROM uses_products
        WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 14
    """)
    analysis['growth_metrics']['daily_growth'] = [dict(row) for row in cur.fetchall()]
    
    # Hourly pattern (last 24 hours)
    cur.execute("""
        SELECT 
            DATE_TRUNC('hour', created_at) as hour,
            COUNT(*) as products_added
        FROM uses_products
        WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
        GROUP BY DATE_TRUNC('hour', created_at)
        ORDER BY hour DESC
    """)
    analysis['growth_metrics']['hourly_pattern'] = [dict(row) for row in cur.fetchall()]
    
    # 5. Coverage Gaps
    print("\n🔍 Coverage Analysis...")
    
    # Industries with few products
    cur.execute("""
        SELECT i.name as industry, COUNT(p.id) as product_count
        FROM industries i
        LEFT JOIN industry_sub_categories isc ON isc.industry_id = i.id
        LEFT JOIN uses_products p ON p.industry_sub_category_id = isc.id
        GROUP BY i.id, i.name
        HAVING COUNT(p.id) < 20
        ORDER BY product_count
    """)
    analysis['coverage_gaps']['underserved_industries'] = [dict(row) for row in cur.fetchall()]
    
    # Plant parts with few products
    cur.execute("""
        SELECT pp.name as plant_part, COUNT(p.id) as product_count
        FROM plant_parts pp
        LEFT JOIN uses_products p ON p.plant_part_id = pp.id
        GROUP BY pp.id, pp.name
        HAVING COUNT(p.id) < 50
        ORDER BY product_count
    """)
    analysis['coverage_gaps']['underserved_plant_parts'] = [dict(row) for row in cur.fetchall()]
    
    # Products without companies
    cur.execute("""
        SELECT COUNT(*) as count
        FROM uses_products
        WHERE primary_company_id IS NULL
        AND id NOT IN (SELECT product_id FROM hemp_company_products)
    """)
    analysis['coverage_gaps']['products_without_companies'] = cur.fetchone()['count']
    
    # 6. Company Analysis
    print("\n🏢 Company Analysis...")
    
    cur.execute("""
        SELECT 
            COUNT(*) as total_companies,
            COUNT(CASE WHEN logo_url IS NOT NULL THEN 1 END) as with_logos,
            COUNT(CASE WHEN website IS NOT NULL THEN 1 END) as with_websites,
            COUNT(CASE WHEN description IS NOT NULL THEN 1 END) as with_descriptions
        FROM hemp_companies
    """)
    analysis['companies']['overview'] = dict(cur.fetchone())
    
    # Top companies by products
    cur.execute("""
        SELECT 
            c.name as company,
            COUNT(DISTINCT p.id) as product_count
        FROM hemp_companies c
        LEFT JOIN hemp_company_products cp ON cp.company_id = c.id
        LEFT JOIN uses_products p ON p.id = cp.product_id
        GROUP BY c.id, c.name
        ORDER BY product_count DESC
        LIMIT 10
    """)
    analysis['companies']['top_by_products'] = [dict(row) for row in cur.fetchall()]
    
    # 7. AI Impact
    print("\n🤖 AI Impact Analysis...")
    
    cur.execute("""
        SELECT 
            source_agent,
            COUNT(*) as products_created,
            AVG(LENGTH(COALESCE(description, ''))) as avg_description_length,
            COUNT(CASE WHEN benefits_advantages IS NOT NULL THEN 1 END) as with_benefits,
            COUNT(CASE WHEN technical_specifications IS NOT NULL THEN 1 END) as with_tech_specs
        FROM uses_products
        WHERE source_agent IS NOT NULL
        GROUP BY source_agent
        ORDER BY products_created DESC
    """)
    analysis['ai_impact']['by_agent'] = [dict(row) for row in cur.fetchall()]
    
    # Save detailed report
    with open('database_analysis_report.json', 'w') as f:
        json.dump(analysis, f, indent=2, default=str)
    
    cur.close()
    conn.close()
    
    return analysis

def print_analysis_summary(analysis):
    """Print a formatted summary of the analysis"""
    
    print("\n" + "="*60)
    print("📊 INDUSTRIAL HEMP DATABASE ANALYSIS SUMMARY")
    print("="*60)
    
    # Overall Statistics
    print(f"\n🌿 DATABASE SCALE:")
    print(f"   Total Products: {analysis['summary']['total_products']:,}")
    print(f"   Total Companies: {analysis['summary']['total_companies']:,}")
    print(f"   Research Papers: {analysis['summary']['total_research']:,}")
    print(f"   Plant Parts Covered: {analysis['summary']['plant_parts_covered']}/8")
    print(f"   Industries Covered: {analysis['summary']['industries_covered']}")
    
    # Product Distribution
    print(f"\n📦 PRODUCT DISTRIBUTION:")
    for item in analysis['products']['by_plant_part'][:5]:
        print(f"   {item['plant_part']}: {item['count']} products")
    
    # Quality Metrics
    completeness = analysis['products']['completeness']
    total = completeness['total']
    print(f"\n✨ QUALITY METRICS:")
    print(f"   With Descriptions: {completeness['with_description']}/{total} ({completeness['with_description']/total*100:.1f}%)")
    print(f"   With Benefits: {completeness['with_benefits']}/{total} ({completeness['with_benefits']/total*100:.1f}%)")
    print(f"   With Images: {completeness['with_images']}/{total} ({completeness['with_images']/total*100:.1f}%)")
    print(f"   With Companies: {completeness['with_company']}/{total} ({completeness['with_company']/total*100:.1f}%)")
    print(f"   AI Generated: {completeness['ai_generated']}/{total} ({completeness['ai_generated']/total*100:.1f}%)")
    
    # Growth Rate
    recent_growth = analysis['growth_metrics']['daily_growth']
    if recent_growth:
        today = recent_growth[0]['products_added'] if recent_growth else 0
        week_total = sum(day['products_added'] for day in recent_growth[:7])
        print(f"\n📈 GROWTH METRICS:")
        print(f"   Added Today: {today}")
        print(f"   Added This Week: {week_total}")
        print(f"   Average Daily: {week_total/7:.1f}")
    
    # Coverage Gaps
    print(f"\n🔍 COVERAGE GAPS:")
    gaps = analysis['coverage_gaps']
    if gaps['underserved_industries']:
        print(f"   Industries needing products: {len(gaps['underserved_industries'])}")
    if gaps['underserved_plant_parts']:
        print(f"   Plant parts needing products: {len(gaps['underserved_plant_parts'])}")
    print(f"   Products without companies: {gaps['products_without_companies']}")
    
    # AI Impact
    print(f"\n🤖 AI AGENT IMPACT:")
    for agent in analysis['ai_impact']['by_agent'][:3]:
        print(f"   {agent['source_agent'] or 'Manual'}: {agent['products_created']} products")

if __name__ == "__main__":
    analysis = analyze_database()
    print_analysis_summary(analysis)
    print(f"\n📄 Detailed report saved to: database_analysis_report.json")