#!/usr/bin/env python3
"""Debug DeepSeek provider"""

import asyncio
import logging
import os

logging.basicConfig(level=logging.DEBUG)

from utils.ai_providers import DeepSeekProvider, MultiProviderAI


async def test_deepseek_direct():
    """Test DeepSeek directly"""
    print("\n=== Testing DeepSeek Directly ===")
    
    deepseek = DeepSeekProvider()
    try:
        result = await deepseek.generate("Say hello in 5 words")
        print(f"Success! Result: {result}")
    except Exception as e:
        print(f"Failed: {e}")


async def test_multi_provider():
    """Test MultiProviderAI with DeepSeek"""
    print("\n=== Testing MultiProviderAI with DeepSeek ===")
    
    multi = MultiProviderAI(primary_provider="deepseek", fallback_providers=["openai"])
    print(f"Providers initialized: {len(multi.providers)}")
    
    for i, provider in enumerate(multi.providers):
        print(f"  Provider {i}: {provider.__class__.__name__}")
    
    try:
        result, provider_name, cost = await multi.generate("Say hello in 5 words")
        print(f"Success! Used: {provider_name}")
        print(f"Result: {result}")
        print(f"Cost: ${cost:.6f}")
    except Exception as e:
        print(f"Failed: {e}")


async def main():
    await test_deepseek_direct()
    await test_multi_provider()


if __name__ == "__main__":
    asyncio.run(main())