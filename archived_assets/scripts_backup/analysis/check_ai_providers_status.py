#!/usr/bin/env python3
"""
AI Provider Status Checker
Tests all configured AI providers to diagnose connectivity and API issues
"""
import os
import time
import json
from datetime import datetime
from typing import Dict, Any, Tuple
import sys

# Add the ai_providers directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_deepseek():
    """Test DeepSeek API connectivity and functionality"""
    try:
        from ai_providers.multi_provider import DeepSeekProvider
        
        print("🔍 Testing DeepSeek API...")
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            print("❌ DeepSeek: No API key configured")
            return False, 0
            
        provider = DeepSeekProvider(api_key)
        
        # Test with a simple prompt
        start_time = time.time()
        response = provider.generate("Say 'DeepSeek API is working!' and nothing else.")
        elapsed = time.time() - start_time
        
        if response.success and "working" in response.content.lower():
            print(f"✅ DeepSeek: SUCCESS (Response time: {elapsed:.2f}s)")
            print(f"   Response: {response.content.strip()}")
            return True, elapsed
        else:
            print(f"❌ DeepSeek: FAILED")
            print(f"   Error: {response.error}")
            print(f"   Response: {response.content}")
            return False, elapsed
            
    except Exception as e:
        print(f"❌ DeepSeek: ERROR - {str(e)}")
        return False, 0

def test_gemini():
    """Test Gemini API connectivity and functionality"""
    try:
        from ai_providers.multi_provider import GeminiProvider
        
        print("\n🔍 Testing Gemini API...")
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            print("❌ Gemini: No API key configured")
            return False, 0
            
        provider = GeminiProvider(api_key)
        
        # Test with a simple prompt
        start_time = time.time()
        response = provider.generate("Say 'Gemini API is working!' and nothing else.")
        elapsed = time.time() - start_time
        
        if response.success and "working" in response.content.lower():
            print(f"✅ Gemini: SUCCESS (Response time: {elapsed:.2f}s)")
            print(f"   Response: {response.content.strip()}")
            return True, elapsed
        else:
            print(f"❌ Gemini: FAILED")
            print(f"   Error: {response.error}")
            print(f"   Response: {response.content}")
            return False, elapsed
            
    except Exception as e:
        print(f"❌ Gemini: ERROR - {str(e)}")
        return False, 0

def test_openai():
    """Test OpenAI API connectivity and functionality"""
    try:
        from ai_providers.multi_provider import OpenAIProvider
        
        print("\n🔍 Testing OpenAI API...")
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ OpenAI: No API key configured")
            return False, 0
            
        provider = OpenAIProvider(api_key)
        
        # Test with a simple prompt
        start_time = time.time()
        response = provider.generate("Say 'OpenAI API is working!' and nothing else.")
        elapsed = time.time() - start_time
        
        if response.success and "working" in response.content.lower():
            print(f"✅ OpenAI: SUCCESS (Response time: {elapsed:.2f}s)")
            print(f"   Response: {response.content.strip()}")
            return True, elapsed
        else:
            print(f"❌ OpenAI: FAILED")
            print(f"   Error: {response.error}")
            print(f"   Response: {response.content}")
            return False, elapsed
            
    except Exception as e:
        print(f"❌ OpenAI: ERROR - {str(e)}")
        return False, 0

def test_multi_provider():
    """Test the multi-provider fallback system"""
    try:
        from ai_providers.multi_provider import MultiProviderAI
        
        print("\n🔍 Testing Multi-Provider System...")
        client = MultiProviderAI()
        
        # Test with a simple prompt
        start_time = time.time()
        response = client.generate("Say 'Multi-provider system is working!' and nothing else.")
        elapsed = time.time() - start_time
        
        if response.success and "working" in response.content.lower():
            print(f"✅ Multi-Provider: SUCCESS (Response time: {elapsed:.2f}s)")
            print(f"   Active Provider: {response.provider}")
            print(f"   Response: {response.content.strip()}")
            return True, elapsed
        else:
            print(f"❌ Multi-Provider: FAILED")
            print(f"   Error: {response.error}")
            print(f"   Response: {response.content}")
            return False, elapsed
            
    except Exception as e:
        print(f"❌ Multi-Provider: ERROR - {str(e)}")
        return False, 0

def check_api_keys():
    """Check if API keys are configured"""
    print("\n🔑 Checking API Key Configuration:")
    
    keys = {
        "DEEPSEEK_API_KEY": os.getenv("DEEPSEEK_API_KEY"),
        "GEMINI_API_KEY": os.getenv("GEMINI_API_KEY"),
        "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY")
    }
    
    for key_name, key_value in keys.items():
        if key_value:
            print(f"✅ {key_name}: Configured (length: {len(key_value)})")
        else:
            print(f"❌ {key_name}: NOT CONFIGURED")
    
    return keys

def test_product_generation():
    """Test actual product generation to simulate automation"""
    try:
        from ai_providers.multi_provider import MultiProviderAI
        
        print("\n🔍 Testing Product Generation...")
        client = MultiProviderAI()
        
        prompt = """Generate ONE innovative hemp product. Format:
Product Name: [name]
Description: [50+ characters]
Benefits: [3 benefits]
Technical Specs: [2 specs]"""
        
        start_time = time.time()
        response = client.generate(prompt)
        elapsed = time.time() - start_time
        
        if response.success and "Product Name:" in response.content:
            print(f"✅ Product Generation: SUCCESS (Response time: {elapsed:.2f}s)")
            print(f"   Provider: {response.provider}")
            print(f"   Sample Output:\n{response.content[:200]}...")
            return True, elapsed
        else:
            print(f"❌ Product Generation: FAILED")
            print(f"   Error: {response.error}")
            print(f"   Response: {response.content}")
            return False, elapsed
            
    except Exception as e:
        print(f"❌ Product Generation: ERROR - {str(e)}")
        return False, 0

def main():
    """Run all tests and provide summary"""
    print("=" * 60)
    print("🤖 AI PROVIDER STATUS CHECK")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Check API keys first
    keys = check_api_keys()
    
    # Run individual provider tests
    results = {}
    results['deepseek'] = test_deepseek()
    results['gemini'] = test_gemini()
    results['openai'] = test_openai()
    results['multi_provider'] = test_multi_provider()
    results['product_generation'] = test_product_generation()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    working_providers = sum(1 for success, _ in results.values() if success)
    total_providers = len(results)
    
    print(f"\n✅ Working: {working_providers}/{total_providers}")
    print(f"❌ Failed: {total_providers - working_providers}/{total_providers}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    
    if results['deepseek'][0]:
        print("✅ DeepSeek is working - primary provider is healthy")
    else:
        print("⚠️  DeepSeek is down - fallback providers will be used")
    
    if not results['multi_provider'][0]:
        print("🚨 CRITICAL: Multi-provider system is failing!")
        print("   This explains why automation is stuck at 583 products")
    
    if not results['product_generation'][0]:
        print("🚨 Product generation is failing - automation cannot add new products")
    
    # Write status to file for automation monitoring
    status_file = "ai_provider_status.json"
    with open(status_file, 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'results': {k: {'success': v[0], 'response_time': v[1]} for k, v in results.items()},
            'working_count': working_providers,
            'total_count': total_providers
        }, f, indent=2)
    
    print(f"\n📄 Status saved to: {status_file}")

if __name__ == "__main__":
    main()