#!/usr/bin/env python3
"""
Check current database status without requiring new fields
"""

import asyncio
import asyncpg
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

async def check_status():
    conn = await asyncpg.connect(
        os.getenv('DATABASE_URL'),
        statement_cache_size=0  # Disable for pgbouncer compatibility
    )
    
    try:
        print("\n" + "="*60)
        print(f"🌿 HEMP DATABASE STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        print("="*60)
        
        # Basic metrics
        total_products = await conn.fetchval("SELECT COUNT(*) FROM uses_products")
        total_companies = await conn.fetchval("SELECT COUNT(*) FROM hemp_companies")
        total_research = await conn.fetchval("SELECT COUNT(*) FROM research_entries")
        
        print(f"\n📊 CURRENT DATA:")
        print(f"   Products: {total_products:,}")
        print(f"   Companies: {total_companies:,}")
        print(f"   Research Papers: {total_research:,}")
        
        # Coverage by plant part
        coverage = await conn.fetch("""
            SELECT 
                pp.name as plant_part,
                COUNT(up.id) as product_count
            FROM plant_parts pp
            LEFT JOIN uses_products up ON up.plant_part_id = pp.id
            GROUP BY pp.id, pp.name
            ORDER BY product_count DESC
        """)
        
        print(f"\n🌱 PRODUCTS BY PLANT PART:")
        for row in coverage:
            print(f"   {row['plant_part']}: {row['product_count']} products")
            
        # Recent additions
        recent = await conn.fetchval("""
            SELECT COUNT(*) FROM uses_products 
            WHERE created_at > NOW() - INTERVAL '7 days'
        """)
        
        print(f"\n📈 RECENT ACTIVITY:")
        print(f"   Products added (last 7 days): {recent}")
        
        # Sample products
        samples = await conn.fetch("""
            SELECT name, id FROM uses_products 
            ORDER BY created_at DESC LIMIT 5
        """)
        
        print(f"\n🆕 LATEST PRODUCTS:")
        for p in samples:
            print(f"   [{p['id']}] {p['name']}")
            
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(check_status())