#!/usr/bin/env python3
"""
Check research entry URLs to understand their sources
"""
import os
import requests
from dotenv import load_dotenv

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
}

def analyze_research_urls():
    """Analyze research entry URLs"""
    print("📚 Analyzing research entry URLs...")
    
    # Get all research entries
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/research_entries",
        headers=headers,
        params={"select": "id,title,full_text_url,entry_type"}
    )
    
    entries = response.json()
    print(f"Found {len(entries)} research entries\n")
    
    # Categorize by source
    sources = {}
    
    for entry in entries:
        url = entry.get('full_text_url', '')
        if url:
            # Extract domain
            if 'pubmed' in url or 'ncbi' in url:
                source = 'PubMed/NCBI'
            elif 'hempindustrydaily' in url:
                source = 'Hemp Industry Daily'
            elif 'votehemp' in url:
                source = 'Vote Hemp'
            elif 'eiha' in url:
                source = 'EIHA'
            else:
                source = 'Other'
        else:
            source = 'No URL'
        
        if source not in sources:
            sources[source] = []
        
        sources[source].append({
            'title': entry['title'][:60] + '...',
            'type': entry.get('entry_type', 'Unknown'),
            'url': url
        })
    
    # Display analysis
    for source, entries in sources.items():
        print(f"\n🔗 {source} ({len(entries)} entries):")
        for entry in entries[:3]:  # Show first 3
            print(f"   - {entry['type']}: {entry['title']}")
            if entry['url']:
                print(f"     URL: {entry['url'][:80]}...")
        
        if len(entries) > 3:
            print(f"   ... and {len(entries) - 3} more")
    
    # Check for PubMed entries specifically
    pubmed_entries = sources.get('PubMed/NCBI', [])
    print(f"\n📊 PubMed entries that could have images: {len(pubmed_entries)}")
    
    return sources

if __name__ == "__main__":
    analyze_research_urls()