#!/usr/bin/env python3
"""Minimal verification script for GitHub Actions status checks - no OpenAI required"""

import os
import sys
from dotenv import load_dotenv
from supabase import create_client

# Load environment variables
load_dotenv()

print("🔍 Running Status Check (OpenAI-free)...")
print("="*50)

# Check only required environment variables for status checks
required_vars = {
    'SUPABASE_URL': os.environ.get('SUPABASE_URL'),
    'SUPABASE_ANON_KEY': os.environ.get('SUPABASE_ANON_KEY')
}

all_set = True
for var, value in required_vars.items():
    if value and value not in ['your_supabase_anon_key_here']:
        print(f"✅ {var}: Set")
    else:
        print(f"❌ {var}: Not set or using placeholder")
        all_set = False

if not all_set:
    print("\n❌ Missing required Supabase credentials")
    sys.exit(1)

# Test Supabase connection
print("\n📡 Testing Supabase connection...")
try:
    supabase = create_client(required_vars['SUPABASE_URL'], required_vars['SUPABASE_ANON_KEY'])
    
    # Check main database tables (not automation-specific)
    main_tables = [
        'uses_products',
        'hemp_companies', 
        'plant_parts',
        'industry_sub_categories'
    ]
    
    print("\n📊 Main Database Status:")
    total_records = 0
    for table in main_tables:
        try:
            result = supabase.table(table).select('*', count='exact').limit(1).execute()
            count = result.count if hasattr(result, 'count') else 0
            total_records += count
            print(f"  ✅ {table}: {count} records")
        except Exception as e:
            print(f"  ⚠️  {table}: Error accessing table")
    
    # Check automation tables (optional)
    print("\n🤖 Automation Tables (optional):")
    automation_tables = [
        'hemp_automation_companies',
        'hemp_automation_products',
        'hemp_agent_runs'
    ]
    
    for table in automation_tables:
        try:
            result = supabase.table(table).select('*', count='exact').limit(1).execute()
            count = result.count if hasattr(result, 'count') else 0
            print(f"  ✅ {table}: {count} records")
        except:
            print(f"  ℹ️  {table}: Not found (OK - only needed for AI agents)")
    
    print(f"\n✅ Status Check Complete!")
    print(f"   Total records in main tables: {total_records}")
    print(f"   Database connection: Healthy")
    
except Exception as e:
    print(f"\n❌ Connection error: {e}")
    print("   Check your Supabase credentials")
    sys.exit(1)