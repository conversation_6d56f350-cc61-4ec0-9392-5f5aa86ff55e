#!/usr/bin/env python3
"""
Update all fallback images to use a Supabase-hosted Unknown Hemp Image
"""
import psycopg2
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

DATABASE_URL = os.getenv('DATABASE_URL')
conn = psycopg2.connect(DATABASE_URL)
cursor = conn.cursor()

print("Searching for Supabase-hosted Unknown Hemp Image...")

# First, let's find if there's a Supabase image we should use
cursor.execute("""
    SELECT DISTINCT image_url 
    FROM uses_products 
    WHERE image_url LIKE 'https://ktoqznqmlnxrtvubewyz.supabase.co%'
    AND (
        LOWER(image_url) LIKE '%unknown%' 
        OR LOWER(image_url) LIKE '%placeholder%'
        OR LOWER(image_url) LIKE '%fallback%'
        OR LOWER(image_url) LIKE '%default%'
    )
    LIMIT 5
""")

supabase_fallbacks = cursor.fetchall()

if supabase_fallbacks:
    print(f"\nFound {len(supabase_fallbacks)} potential Supabase fallback images:")
    for url in supabase_fallbacks:
        print(f"  - {url[0]}")
    
    # Use the first one as the fallback
    SUPABASE_FALLBACK = supabase_fallbacks[0][0]
else:
    # Check for any Supabase image we can use
    cursor.execute("""
        SELECT image_url 
        FROM uses_products 
        WHERE image_url LIKE 'https://ktoqznqmlnxrtvubewyz.supabase.co%'
        AND image_url LIKE '%unknown%'
        ORDER BY id
        LIMIT 1
    """)
    result = cursor.fetchone()
    
    if result:
        SUPABASE_FALLBACK = result[0]
        print(f"\nFound Unknown Hemp Image in Supabase: {SUPABASE_FALLBACK}")
    else:
        print("\nNo Unknown Hemp Image found in Supabase.")
        print("Would you like to upload the local unknown-hemp-image.png to Supabase?")
        cursor.close()
        conn.close()
        exit(1)

# Now update all local fallback images to use the Supabase URL
print(f"\nUpdating fallback images to use: {SUPABASE_FALLBACK}")

# Get all products using local fallback images
cursor.execute("""
    SELECT id, name, image_url 
    FROM uses_products 
    WHERE image_url IN (
        '/images/unknown-hemp-image.png',
        '/images/fiber-hemp.png',
        '/images/grain-hemp.png',
        '/images/cannabinoid-hemp.png',
        '/images/dual-use-hemp.png'
    )
""")

products_to_update = cursor.fetchall()
print(f"\nFound {len(products_to_update)} products using local fallback images")

if products_to_update:
    # Update all to use the Supabase fallback
    product_ids = [p[0] for p in products_to_update]
    
    cursor.execute("""
        UPDATE uses_products 
        SET image_url = %s 
        WHERE id = ANY(%s)
    """, (SUPABASE_FALLBACK, product_ids))
    
    conn.commit()
    print(f"✅ Updated {cursor.rowcount} products to use Supabase fallback image")

# Show final distribution
cursor.execute("""
    SELECT 
        CASE 
            WHEN image_url = %s THEN 'Supabase Unknown Hemp Image'
            WHEN image_url LIKE 'https://ktoqznqmlnxrtvubewyz.supabase.co%' THEN 'Other Supabase Images'
            WHEN image_url LIKE '/generated_images/%' THEN 'Local Generated Images'
            WHEN image_url LIKE '/images/%' THEN 'Local Fallback Images'
            WHEN image_url LIKE 'http%' THEN 'External URLs'
            ELSE 'Other'
        END as image_type,
        COUNT(*) as count
    FROM uses_products
    GROUP BY 1
    ORDER BY count DESC
""", (SUPABASE_FALLBACK,))

print("\n📊 Final Image URL Distribution:")
for row in cursor.fetchall():
    print(f"  {row[0]}: {row[1]} products")

cursor.close()
conn.close()