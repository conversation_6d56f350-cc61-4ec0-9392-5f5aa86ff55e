#!/usr/bin/env python3
"""
Link Products to Companies - Fixed with direct Supabase queries
"""

import os
import re
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher
from supabase import create_client, Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class MatchScore:
    product_id: int
    company_id: int
    name_score: float
    industry_score: float
    description_score: float
    total_score: float
    confidence: str

class ProductCompanyLinker:
    def __init__(self):
        self.supabase = self._init_supabase()
        
    def _init_supabase(self) -> Client:
        """Initialize Supabase client"""
        url = os.getenv('VITE_SUPABASE_URL', 'https://ktoqznqmlnxrtvubewyz.supabase.co')
        key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        if not key:
            key = os.getenv('VITE_SUPABASE_ANON_KEY')
        return create_client(url, key)
    
    def load_unlinked_products(self) -> List[Dict]:
        """Load products without company assignments using direct queries"""
        logger.info("Loading unlinked products...")
        
        # Get all products
        all_products = self.supabase.table('uses_products').select(
            'id, name, description, industry_sub_category_id, plant_part_id'
        ).limit(500).execute()
        
        # Get existing relationships
        existing_links = self.supabase.table('hemp_company_products').select('product_id').execute()
        linked_product_ids = set(link['product_id'] for link in existing_links.data)
        
        # Filter unlinked products
        unlinked_products = [
            product for product in all_products.data 
            if product['id'] not in linked_product_ids
        ]
        
        logger.info(f"Found {len(unlinked_products)} unlinked products out of {len(all_products.data)} total")
        return unlinked_products
    
    def load_companies(self) -> List[Dict]:
        """Load all companies"""
        logger.info("Loading companies...")
        
        result = self.supabase.table('hemp_companies').select(
            'id, name, description, website, primary_focus, company_type'
        ).execute()
        
        companies = result.data
        logger.info(f"Loaded {len(companies)} companies")
        return companies
    
    def calculate_name_similarity(self, product_name: str, company_name: str) -> float:
        """Calculate similarity between product and company names"""
        if not product_name or not company_name:
            return 0.0
            
        # Clean names
        product_clean = re.sub(r'[^\w\s]', '', product_name.lower())
        company_clean = re.sub(r'[^\w\s]', '', company_name.lower())
        
        # Check if company name appears in product name or vice versa
        if company_clean in product_clean or product_clean in company_clean:
            return 0.9
        
        # Check word overlap
        product_words = set(product_clean.split())
        company_words = set(company_clean.split())
        
        # Remove common words
        common_words = {'hemp', 'co', 'inc', 'ltd', 'llc', 'corp', 'company'}
        product_words -= common_words
        company_words -= common_words
        
        if company_words and product_words:
            overlap = len(company_words & product_words)
            if overlap > 0:
                return min(0.8, overlap / len(company_words) * 1.5)
        
        # Use sequence matcher for general similarity
        return SequenceMatcher(None, product_clean, company_clean).ratio() * 0.3
    
    def calculate_description_similarity(self, product: Dict, company: Dict) -> float:
        """Calculate description-based similarity"""
        product_desc = (product.get('description') or '').lower()
        company_desc = (company.get('description') or '').lower()
        
        if not product_desc or not company_desc:
            return 0.0
        
        # Extract key terms (avoid very common words)
        product_terms = set(re.findall(r'\b(?:fiber|oil|seed|textile|food|protein|construction|material|organic|sustainable|extract|isolate|composite|yarn|fabric|insulation|building|cosmetic|supplement|beverage|paper|biofuel|plastic)\w*\b', product_desc))
        company_terms = set(re.findall(r'\b(?:fiber|oil|seed|textile|food|protein|construction|material|organic|sustainable|extract|isolate|composite|yarn|fabric|insulation|building|cosmetic|supplement|beverage|paper|biofuel|plastic)\w*\b', company_desc))
        
        if not company_terms:
            return 0.0
        
        # Calculate overlap
        overlap = len(product_terms & company_terms) / len(company_terms)
        return min(1.0, overlap * 2)
    
    def calculate_industry_score(self, product: Dict, company: Dict) -> float:
        """Calculate industry alignment score based on keywords"""
        score = 0.0
        
        product_name = (product.get('name') or '').lower()
        product_desc = (product.get('description') or '').lower()
        company_focus = (company.get('primary_focus') or '').lower()
        company_desc = (company.get('description') or '').lower()
        
        # Industry keyword matching
        industry_keywords = {
            'textile': ['fiber', 'yarn', 'fabric', 'clothing', 'textile'],
            'food': ['protein', 'oil', 'seed', 'food', 'nutrition', 'supplement'],
            'construction': ['building', 'insulation', 'hempcrete', 'construction', 'material'],
            'cosmetic': ['cosmetic', 'skincare', 'beauty', 'topical'],
            'automotive': ['automotive', 'composite', 'bio-composite', 'car'],
            'paper': ['paper', 'pulp', 'cardboard'],
            'energy': ['biofuel', 'biomass', 'energy']
        }
        
        product_text = f"{product_name} {product_desc}"
        company_text = f"{company_focus} {company_desc}"
        
        for industry, keywords in industry_keywords.items():
            product_matches = sum(1 for kw in keywords if kw in product_text)
            company_matches = sum(1 for kw in keywords if kw in company_text)
            
            if product_matches > 0 and company_matches > 0:
                score = max(score, 0.8)
            elif product_matches > 0 or company_matches > 0:
                score = max(score, 0.3)
        
        return score
    
    def find_best_matches(self, product: Dict, companies: List[Dict], threshold: float = 0.3) -> List[MatchScore]:
        """Find best company matches for a product"""
        matches = []
        
        for company in companies:
            # Calculate individual scores
            name_score = self.calculate_name_similarity(product['name'], company['name'])
            industry_score = self.calculate_industry_score(product, company)
            desc_score = self.calculate_description_similarity(product, company)
            
            # Weighted total score
            total_score = (
                name_score * 0.3 +
                industry_score * 0.5 +
                desc_score * 0.2
            )
            
            if total_score >= threshold:
                # Determine confidence level
                if total_score >= 0.6:
                    confidence = 'high'
                elif total_score >= 0.4:
                    confidence = 'medium'
                else:
                    confidence = 'low'
                
                match = MatchScore(
                    product_id=product['id'],
                    company_id=company['id'],
                    name_score=name_score,
                    industry_score=industry_score,
                    description_score=desc_score,
                    total_score=total_score,
                    confidence=confidence
                )
                matches.append(match)
        
        # Sort by total score
        matches.sort(key=lambda x: x.total_score, reverse=True)
        return matches[:2]  # Return top 2 matches
    
    def create_product_company_links(self, matches: List[MatchScore], auto_approve_threshold: float = 0.5):
        """Create links between products and companies"""
        created_count = 0
        
        for match in matches:
            if match.total_score >= auto_approve_threshold:
                try:
                    # Check if link already exists
                    existing = self.supabase.table('hemp_company_products').select('id').eq(
                        'product_id', match.product_id
                    ).eq('company_id', match.company_id).execute()
                    
                    if not existing.data:
                        # Create link
                        link_data = {
                            'product_id': match.product_id,
                            'company_id': match.company_id,
                            'relationship_type': 'manufacturer' if match.confidence == 'high' else 'supplier',
                            'is_primary': match.total_score >= 0.7,
                            'verified': False,
                            'notes': f"Auto-matched with {match.confidence} confidence (score: {match.total_score:.2f})"
                        }
                        
                        self.supabase.table('hemp_company_products').insert(link_data).execute()
                        created_count += 1
                        
                        logger.info(
                            f"Linked product {match.product_id} -> company {match.company_id} "
                            f"(confidence: {match.confidence}, score: {match.total_score:.2f})"
                        )
                except Exception as e:
                    logger.error(f"Error creating link: {e}")
        
        return created_count
    
    def run_matching_process(self):
        """Main process to match products with companies"""
        logger.info("Starting product-company matching process...")
        
        # Load data
        products = self.load_unlinked_products()
        companies = self.load_companies()
        
        if not products:
            logger.info("No unlinked products found")
            return
        
        if not companies:
            logger.error("No companies found in database")
            return
        
        # Process matching
        total_matches = 0
        high_confidence = 0
        medium_confidence = 0
        low_confidence = 0
        
        batch_size = 50
        for i in range(0, len(products), batch_size):
            batch = products[i:i+batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: products {i+1}-{min(i+batch_size, len(products))}")
            
            for product in batch:
                # Find matches
                matches = self.find_best_matches(product, companies)
                
                if matches:
                    # Create links for qualifying matches
                    created = self.create_product_company_links(matches)
                    total_matches += created
                    
                    # Count confidence levels
                    for match in matches:
                        if match.confidence == 'high':
                            high_confidence += 1
                        elif match.confidence == 'medium':
                            medium_confidence += 1
                        else:
                            low_confidence += 1
        
        # Final report
        logger.info("\n" + "="*50)
        logger.info("MATCHING PROCESS COMPLETE")
        logger.info(f"Products processed: {len(products)}")
        logger.info(f"Total matches created: {total_matches}")
        logger.info(f"High confidence: {high_confidence}")
        logger.info(f"Medium confidence: {medium_confidence}")
        logger.info(f"Low confidence: {low_confidence}")
        logger.info("="*50)
        
        return {
            'products_processed': len(products),
            'matches_created': total_matches,
            'high_confidence': high_confidence,
            'medium_confidence': medium_confidence,
            'low_confidence': low_confidence
        }

def main():
    """Main entry point"""
    linker = ProductCompanyLinker()
    results = linker.run_matching_process()
    
    if results and results['matches_created'] > 0:
        logger.info(f"\n✅ Successfully linked {results['matches_created']} products to companies!")
        logger.info("Run the script again to process more batches of unlinked products.")

if __name__ == "__main__":
    main()