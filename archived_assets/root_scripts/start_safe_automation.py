#!/usr/bin/env python3
"""
Safe Automation Starter - Runs automation with comprehensive quality controls
Ensures no duplicates, no ** formatting, and high-quality entries only
"""
import os
import sys
import time
import schedule
import logging
from datetime import datetime
from pathlib import Path

# Import the safe automation runner
from safe_automation_runner import SafeAutomationRunner

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/safe_automation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

def run_automation_cycle():
    """Run one complete automation cycle with quality checks"""
    logging.info("=" * 80)
    logging.info("🚀 STARTING SAFE AUTOMATION CYCLE")
    logging.info("=" * 80)
    
    try:
        runner = SafeAutomationRunner()
        
        # Check current quality before starting
        initial_metrics = runner.check_current_quality()
        logging.info(f"Initial quality check: {initial_metrics}")
        
        if initial_metrics['formatting_issues'] > 0 or initial_metrics.get('duplicate_groups', 0) > 0:
            logging.info("⚠️ Quality issues detected, running cleanup first...")
            runner.run_cleanup()
        
        # Run the automation cycle
        runner.run_cycle()
        
        # Close database connection if method exists
        if hasattr(runner, 'close'):
            runner.close()
        elif hasattr(runner, 'conn'):
            runner.conn.close()
            
        logging.info("✅ Automation cycle completed successfully")
        
    except Exception as e:
        logging.error(f"❌ Automation cycle failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main automation loop"""
    # Command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Safe Hemp Database Automation')
    parser.add_argument('--continuous', action='store_true', help='Run continuously every hour')
    parser.add_argument('--run', action='store_true', help='Run once and exit')
    parser.add_argument('--interval', type=int, default=1, help='Hours between runs (default: 1)')
    args = parser.parse_args()
    
    logging.info("🌿 Hemp Database Safe Automation Starting...")
    logging.info(f"Mode: {'Continuous' if args.continuous else 'Single run'}")
    
    if args.run or not args.continuous:
        # Run once
        run_automation_cycle()
    else:
        # Schedule continuous runs
        logging.info(f"Scheduling automation every {args.interval} hours")
        
        # Run immediately
        run_automation_cycle()
        
        # Schedule future runs
        schedule.every(args.interval).hours.do(run_automation_cycle)
        
        # Keep running
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

if __name__ == "__main__":
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    # Verify all required files exist
    required_files = [
        'safe_automation_runner.py',
        'enhanced_quality_control_pipeline.py',
        'working_product_adder_v2.py'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        logging.error(f"❌ Missing required files: {missing_files}")
        sys.exit(1)
    
    # Start automation
    main()