#!/usr/bin/env python3
"""
Fix cannabis terminology in products
"""
import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

# Replacement mapping
REPLACEMENTS = {
    'cannabis leaf': 'hemp leaf',
    'cannabis leaves': 'hemp leaves',
    'cannabis plant': 'hemp plant',
    'cannabis flower': 'hemp flower',
    'marijuana': 'hemp',
    'weed': 'hemp',
    'ganja': 'hemp',
    'recreational cannabis': 'industrial hemp',
    'medical marijuana': 'hemp extract',
    'thc product': 'hemp product',
    'psychoactive': 'non-psychoactive'
}

def fix_terminology():
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    fixed_count = 0
    
    for old_term, new_term in REPLACEMENTS.items():
        # Fix names
        cursor.execute("""
            UPDATE uses_products
            SET name = REPLACE(name, %s, %s),
                updated_at = NOW()
            WHERE LOWER(name) LIKE %s
        """, (old_term, new_term, f'%{old_term.lower()}%'))
        
        name_updates = cursor.rowcount
        
        # Fix descriptions
        cursor.execute("""
            UPDATE uses_products
            SET description = REPLACE(description, %s, %s),
                updated_at = NOW()
            WHERE LOWER(description) LIKE %s
        """, (old_term, new_term, f'%{old_term.lower()}%'))
        
        desc_updates = cursor.rowcount
        
        if name_updates > 0 or desc_updates > 0:
            print(f"Replaced '{old_term}' -> '{new_term}': {name_updates} names, {desc_updates} descriptions")
            fixed_count += name_updates + desc_updates
    
    conn.commit()
    print(f"\nTotal fixes applied: {fixed_count}")
    conn.close()

if __name__ == "__main__":
    print("Fixing cannabis terminology...")
    fix_terminology()
