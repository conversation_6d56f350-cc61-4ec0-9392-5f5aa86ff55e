#!/usr/bin/env python3
"""
Target Underrepresented Industries - Focus on critical gaps in the database
"""

import os
import json
import psycopg2
from datetime import datetime
import random

DATABASE_URL = os.environ.get('DATABASE_URL')

# Critical industries with 0-5 products
CRITICAL_INDUSTRIES = {
    "aerospace": {
        "name": "Aerospace Industry",
        "products": [
            "Hemp Composite Aircraft Interior Panels",
            "Hemp-Based Aerospace Insulation Material",
            "Hemp Fiber Reinforced Satellite Components",
            "Hemp Carbon Fiber Space Applications",
            "Lightweight Hemp Drone Components"
        ],
        "descriptions": [
            "Ultra-lightweight hemp composite materials for aircraft interiors meeting FAA fire safety standards",
            "Advanced thermal insulation using hemp fibers for aerospace applications with extreme temperature resistance",
            "Satellite components utilizing hemp-derived carbon fiber for reduced weight and enhanced durability",
            "Space-grade hemp carbon fiber materials for orbital applications with radiation resistance",
            "High-strength hemp composites for unmanned aerial vehicle construction"
        ]
    },
    "medical_supplies": {
        "name": "Medical Supplies",
        "products": [
            "Hemp-Based Medical Gauze",
            "Hemp Surgical Masks",
            "Hemp Medical Tape",
            "Hemp Bandage Material",
            "Hemp Medical Gowns"
        ],
        "descriptions": [
            "Sterile medical gauze made from hemp fibers with natural antimicrobial properties",
            "Breathable surgical masks utilizing hemp fiber filtration layers",
            "Hypoallergenic medical tape using hemp-based adhesives",
            "Absorbent bandage material from hemp with enhanced healing properties",
            "Disposable medical gowns made from hemp non-woven fabric"
        ]
    },
    "electronics": {
        "name": "Consumer Goods & Electronics",
        "products": [
            "Hemp Bioplastic Phone Cases",
            "Hemp Circuit Board Substrates",
            "Hemp-Based Cable Insulation",
            "Hemp Electronic Component Housing",
            "Hemp Acoustic Speaker Cones"
        ],
        "descriptions": [
            "Durable phone cases made from hemp bioplastic with shock absorption",
            "Eco-friendly circuit board substrates using hemp-derived materials",
            "Electrical cable insulation from hemp with excellent dielectric properties",
            "Heat-resistant housing for electronic components made from hemp composites",
            "High-fidelity speaker cones utilizing hemp fiber for superior sound quality"
        ]
    },
    "packaging": {
        "name": "Packaging Industry",
        "products": [
            "Hemp Molded Packaging Inserts",
            "Hemp Bubble Wrap Alternative",
            "Hemp Food Container Packaging",
            "Hemp Shipping Boxes",
            "Hemp Protective Film Wrap"
        ],
        "descriptions": [
            "Custom molded packaging inserts from hemp pulp for product protection",
            "Biodegradable bubble wrap alternative using hemp air cushions",
            "Food-safe containers made from hemp bioplastic for sustainable packaging",
            "Corrugated shipping boxes from hemp fiber with enhanced strength",
            "Protective film wrap from hemp cellulose for product wrapping"
        ]
    },
    "automotive": {
        "name": "Automotive Industry",
        "products": [
            "Hemp Car Door Panels",
            "Hemp Dashboard Components",
            "Hemp Seat Backing Material",
            "Hemp Trunk Liners",
            "Hemp Hood Insulation"
        ],
        "descriptions": [
            "Lightweight door panels using hemp composite materials for weight reduction",
            "Dashboard components from hemp bioplastic with UV resistance",
            "Durable seat backing material utilizing hemp fibers for comfort",
            "Trunk liner panels made from compressed hemp for noise reduction",
            "Engine hood insulation using hemp for heat and sound dampening"
        ]
    }
}

def get_plant_part_id(product_type):
    """Map product type to appropriate plant part"""
    mapping = {
        "fiber": 1,  # Hemp Bast
        "composite": 1,
        "plastic": 1,
        "medical": 1,
        "packaging": 7,  # Hemp Hurd
        "insulation": 7,
        "electronic": 1
    }
    
    for key in mapping:
        if key in product_type.lower():
            return mapping[key]
    return 1  # Default to fiber

def get_industry_subcategory_id(industry_name):
    """Get or create industry subcategory"""
    conn = psycopg2.connect(DATABASE_URL)
    cur = conn.cursor()
    
    try:
        # First check if industry exists
        cur.execute("""
            SELECT id FROM industries WHERE name = %s
        """, (industry_name,))
        
        industry = cur.fetchone()
        
        if not industry:
            # Create new industry
            cur.execute("""
                INSERT INTO industries (name, description, created_at)
                VALUES (%s, %s, %s)
                RETURNING id
            """, (industry_name, f"Products for the {industry_name}", datetime.now()))
            
            industry_id = cur.fetchone()[0]
        else:
            industry_id = industry[0]
        
        # Check for subcategory
        cur.execute("""
            SELECT id FROM industry_sub_categories 
            WHERE industry_id = %s 
            LIMIT 1
        """, (industry_id,))
        
        subcategory = cur.fetchone()
        
        if not subcategory:
            # Create default subcategory
            cur.execute("""
                INSERT INTO industry_sub_categories (name, description, industry_id, created_at)
                VALUES (%s, %s, %s, %s)
                RETURNING id
            """, (f"General {industry_name}", f"General products for {industry_name}", 
                  industry_id, datetime.now()))
            
            subcategory_id = cur.fetchone()[0]
        else:
            subcategory_id = subcategory[0]
        
        conn.commit()
        return subcategory_id
        
    except Exception as e:
        conn.rollback()
        print(f"Error: {e}")
        return None
    finally:
        cur.close()
        conn.close()

def add_products_for_industry(industry_key, industry_data):
    """Add products for a specific underrepresented industry"""
    conn = psycopg2.connect(DATABASE_URL)
    cur = conn.cursor()
    
    print(f"\nAdding products for {industry_data['name']}...")
    
    subcategory_id = get_industry_subcategory_id(industry_data['name'])
    if not subcategory_id:
        print(f"Failed to get subcategory for {industry_data['name']}")
        return
    
    added = 0
    for i, product_name in enumerate(industry_data['products']):
        try:
            plant_part_id = get_plant_part_id(product_name)
            
            # Generate comprehensive benefits
            benefits = [
                "Sustainable alternative to traditional materials",
                f"Designed specifically for {industry_data['name'].lower()} applications",
                "Reduced environmental impact",
                "Cost-effective manufacturing",
                "Meeting industry standards and certifications"
            ]
            
            # Technical specifications
            tech_specs = {
                "material_source": "Industrial hemp",
                "processing_method": "Advanced manufacturing",
                "industry_compliance": "Meets industry standards",
                "sustainability_rating": "High",
                "production_scale": "Commercial"
            }
            
            cur.execute("""
                INSERT INTO uses_products (
                    name, description, plant_part_id,
                    industry_sub_category_id, benefits_advantages,
                    technical_specifications, source_agent,
                    confidence_score, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (name) DO NOTHING
                RETURNING id
            """, (
                product_name,
                industry_data['descriptions'][i],
                plant_part_id,
                subcategory_id,
                benefits,
                json.dumps(tech_specs),
                "Industry Gap Filler Agent",
                0.85,
                datetime.now()
            ))
            
            result = cur.fetchone()
            if result:
                added += 1
                print(f"  ✓ Added: {product_name}")
            else:
                print(f"  - Skipped (exists): {product_name}")
                
        except Exception as e:
            print(f"  ✗ Error adding {product_name}: {e}")
            conn.rollback()
            continue
    
    conn.commit()
    conn.close()
    
    print(f"Added {added} products for {industry_data['name']}")
    return added

def main():
    """Fill critical industry gaps"""
    print("=== Targeting Underrepresented Industries ===")
    print(f"Found {len(CRITICAL_INDUSTRIES)} critical industries to address")
    
    total_added = 0
    
    for industry_key, industry_data in CRITICAL_INDUSTRIES.items():
        added = add_products_for_industry(industry_key, industry_data)
        if added:
            total_added += added
    
    print(f"\n✅ Total products added: {total_added}")
    print("Industry gap filling complete!")

if __name__ == "__main__":
    main()