#!/usr/bin/env python3
"""
Merge near-duplicate products intelligently
Keeps the best version and consolidates data
"""
import os
import psycopg2
from datetime import datetime
from difflib import SequenceMatcher
import json
from dotenv import load_dotenv

load_dotenv()

class DuplicateMerger:
    def __init__(self):
        self.conn = psycopg2.connect(os.getenv('DATABASE_URL'))
        self.cursor = self.conn.cursor()
        self.merge_stats = {
            'pairs_found': 0,
            'merged': 0,
            'skipped': 0,
            'errors': 0
        }
        
    def find_duplicate_pairs(self, threshold=0.85):
        """Find all near-duplicate pairs above similarity threshold"""
        print(f"\n🔍 FINDING DUPLICATE PAIRS (>{threshold*100}% similarity)")
        print("="*60)
        
        # Get all products
        self.cursor.execute("""
            SELECT id, name, description, benefits_advantages,
                   plant_part_id, industry_sub_category_id,
                   data_completeness_score, created_at, image_url
            FROM uses_products
            ORDER BY created_at DESC
        """)
        
        products = self.cursor.fetchall()
        duplicate_pairs = []
        checked = set()
        
        for i, prod1 in enumerate(products):
            if i % 100 == 0:
                print(f"Checking product {i}/{len(products)}...")
                
            for j, prod2 in enumerate(products[i+1:], i+1):
                # Skip if already checked
                pair_key = tuple(sorted([prod1[0], prod2[0]]))
                if pair_key in checked:
                    continue
                checked.add(pair_key)
                
                # Check similarity
                name_sim = SequenceMatcher(None, prod1[1].lower(), prod2[1].lower()).ratio()
                
                # Only consider if same plant part and high name similarity
                if prod1[4] == prod2[4] and name_sim >= threshold:
                    duplicate_pairs.append({
                        'product1': prod1,
                        'product2': prod2,
                        'similarity': name_sim,
                        'same_industry': prod1[5] == prod2[5]
                    })
        
        self.merge_stats['pairs_found'] = len(duplicate_pairs)
        print(f"\nFound {len(duplicate_pairs)} duplicate pairs")
        
        return duplicate_pairs
    
    def determine_keeper(self, prod1, prod2):
        """Determine which product to keep based on quality"""
        # Extract fields
        id1, name1, desc1, benefits1, part1, ind1, score1, created1, img1 = prod1
        id2, name2, desc2, benefits2, part2, ind2, score2, created2, img2 = prod2
        
        # Scoring system
        points1 = 0
        points2 = 0
        
        # Completeness score (most important)
        if score1 and score2:
            if score1 > score2:
                points1 += 3
            elif score2 > score1:
                points2 += 3
        elif score1:
            points1 += 3
        elif score2:
            points2 += 3
            
        # Description length
        if desc1 and desc2:
            if len(desc1) > len(desc2):
                points1 += 2
            else:
                points2 += 2
        elif desc1:
            points1 += 2
        elif desc2:
            points2 += 2
            
        # Benefits count
        if benefits1 and benefits2:
            if len(benefits1) > len(benefits2):
                points1 += 1
            elif len(benefits2) > len(benefits1):
                points2 += 1
                
        # Has image
        if img1 and not img2:
            points1 += 1
        elif img2 and not img1:
            points2 += 1
            
        # Industry category assigned
        if ind1 and not ind2:
            points1 += 1
        elif ind2 and not ind1:
            points2 += 1
            
        # If tied, keep older one (more established)
        if points1 == points2:
            if created1 < created2:
                return prod1, prod2
            else:
                return prod2, prod1
                
        # Return winner as keeper, loser as to_delete
        if points1 > points2:
            return prod1, prod2
        else:
            return prod2, prod1
    
    def merge_pair(self, keeper, to_delete):
        """Merge data from to_delete into keeper"""
        keeper_id = keeper[0]
        delete_id = to_delete[0]
        
        try:
            # Get current data
            self.cursor.execute("""
                SELECT description, benefits_advantages, image_url,
                       industry_sub_category_id, data_completeness_score
                FROM uses_products WHERE id = %s
            """, (keeper_id,))
            
            current = self.cursor.fetchone()
            
            # Determine what to update
            updates = []
            params = []
            
            # Merge description if keeper's is shorter
            if to_delete[2] and (not current[0] or len(to_delete[2]) > len(current[0])):
                updates.append("description = %s")
                params.append(to_delete[2])
                
            # Merge benefits if keeper has fewer
            if to_delete[3] and (not current[1] or len(to_delete[3]) > len(current[1])):
                updates.append("benefits_advantages = %s")
                params.append(to_delete[3])
                
            # Take image if keeper doesn't have one
            if to_delete[8] and not current[2]:
                updates.append("image_url = %s")
                params.append(to_delete[8])
                
            # Take industry if keeper doesn't have one
            if to_delete[5] and not current[3]:
                updates.append("industry_sub_category_id = %s")
                params.append(to_delete[5])
                
            # Update completeness score
            updates.append("data_completeness_score = GREATEST(data_completeness_score, %s)")
            params.append(to_delete[6] or 0)
            
            # Apply updates if any
            if updates:
                updates.append("updated_at = NOW()")
                query = f"UPDATE uses_products SET {', '.join(updates)} WHERE id = %s"
                params.append(keeper_id)
                
                self.cursor.execute(query, params)
            
            # Delete the duplicate
            self.cursor.execute("DELETE FROM uses_products WHERE id = %s", (delete_id,))
            
            self.conn.commit()
            return True
            
        except Exception as e:
            self.conn.rollback()
            print(f"Error merging {keeper[1]} and {to_delete[1]}: {e}")
            return False
    
    def run_merge(self, interactive=True, auto_threshold=0.95):
        """Run the merge process"""
        # Find duplicates
        pairs = self.find_duplicate_pairs()
        
        if not pairs:
            print("No duplicates found!")
            return
            
        # Sort by similarity (highest first)
        pairs.sort(key=lambda x: x['similarity'], reverse=True)
        
        print(f"\n🔄 PROCESSING DUPLICATE PAIRS")
        print("="*60)
        
        for i, pair in enumerate(pairs):
            prod1 = pair['product1']
            prod2 = pair['product2']
            similarity = pair['similarity']
            
            print(f"\n[{i+1}/{len(pairs)}] Similarity: {similarity*100:.1f}%")
            print(f"Product 1: {prod1[1]} (ID: {prod1[0]}, Score: {prod1[6]})")
            print(f"Product 2: {prod2[1]} (ID: {prod2[0]}, Score: {prod2[6]})")
            
            # Auto-merge if very high similarity
            if similarity >= auto_threshold:
                print(f"Auto-merging (>{auto_threshold*100}% similar)")
                keeper, to_delete = self.determine_keeper(prod1, prod2)
                
                if self.merge_pair(keeper, to_delete):
                    print(f"✅ Merged: Kept ID {keeper[0]}, deleted ID {to_delete[0]}")
                    self.merge_stats['merged'] += 1
                else:
                    self.merge_stats['errors'] += 1
                    
            elif interactive:
                # Ask user
                action = input("\nAction? (m)erge, (s)kip, (q)uit: ").lower()
                
                if action == 'q':
                    break
                elif action == 'm':
                    keeper, to_delete = self.determine_keeper(prod1, prod2)
                    print(f"Keeping: {keeper[1]} (ID: {keeper[0]})")
                    
                    if self.merge_pair(keeper, to_delete):
                        print("✅ Merged successfully")
                        self.merge_stats['merged'] += 1
                    else:
                        self.merge_stats['errors'] += 1
                else:
                    print("⏭️  Skipped")
                    self.merge_stats['skipped'] += 1
            else:
                # Non-interactive mode - only merge very high similarity
                if similarity >= 0.9:
                    keeper, to_delete = self.determine_keeper(prod1, prod2)
                    
                    if self.merge_pair(keeper, to_delete):
                        print(f"✅ Merged: {keeper[1]}")
                        self.merge_stats['merged'] += 1
                    else:
                        self.merge_stats['errors'] += 1
                else:
                    self.merge_stats['skipped'] += 1
    
    def generate_report(self):
        """Generate merge report"""
        print("\n\n📊 DUPLICATE MERGE REPORT")
        print("="*60)
        
        print(f"Duplicate pairs found: {self.merge_stats['pairs_found']}")
        print(f"Successfully merged: {self.merge_stats['merged']}")
        print(f"Skipped: {self.merge_stats['skipped']}")
        print(f"Errors: {self.merge_stats['errors']}")
        
        # Get current stats
        self.cursor.execute("""
            SELECT COUNT(*) as total,
                   COUNT(DISTINCT LOWER(name)) as unique_names
            FROM uses_products
        """)
        
        total, unique = self.cursor.fetchone()
        print(f"\nDatabase after merge:")
        print(f"Total products: {total}")
        print(f"Unique product names: {unique}")
        print(f"Duplicate rate: {(total-unique)/total*100:.1f}%")
    
    def close(self):
        self.conn.close()


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Merge near-duplicate products')
    parser.add_argument('--auto', action='store_true', 
                       help='Run in automatic mode (no prompts)')
    parser.add_argument('--threshold', type=float, default=0.85,
                       help='Similarity threshold (0.0-1.0, default: 0.85)')
    parser.add_argument('--auto-threshold', type=float, default=0.95,
                       help='Auto-merge threshold (default: 0.95)')
    args = parser.parse_args()
    
    merger = DuplicateMerger()
    
    try:
        merger.run_merge(
            interactive=not args.auto,
            auto_threshold=args.auto_threshold
        )
        merger.generate_report()
    finally:
        merger.close()


if __name__ == "__main__":
    main()