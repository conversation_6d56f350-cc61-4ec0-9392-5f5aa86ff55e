#!/usr/bin/env python3
"""
Comprehensive duplicate detection and cleanup for hemp products database
Identifies and fixes near-duplicates and improves future prevention
"""

import re
import json
from typing import Dict, List, Tuple, Set
from datetime import datetime
import requests
from difflib import SequenceMatcher

class DuplicateAnalyzer:
    def __init__(self):
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Common duplicate patterns
        self.duplicate_patterns = {
            'double_words': r'\b(\w+)\s+\1\b',  # "Cannabinoid Cannabinoid"
            'variation_letters': r'\s+(Plus|Pro|Elite|Ultra|Max|Prime|Select|Premium)\s+[A-Z]\s+',  # "Plus X"
            'double_modifiers': r'\b(Premium|Plus|Pro|Elite|Ultra|Select|Max|Prime)\s+(Premium|Plus|Pro|Elite|Ultra|Select|Max|Prime)\b',
            'redundant_suffixes': r'(Product|Material|Textile|Plastic|Formulation)\s+\1',
        }
        
        # Word substitutions that are essentially the same
        self.equivalent_terms = {
            'Hemp-Based': 'Hemp',
            'Hemp-Derived': 'Hemp',
            'Cannabinoid': 'CBD',
            'Formulation': 'Formula',
            'Manufacturing': 'Production',
            'Process': 'Method',
        }

    def fetch_all_products(self) -> List[Dict]:
        """Fetch all products from database"""
        print("Fetching all products...")
        all_products = []
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "id,name,description,source_agent,created_at,plant_part_id,industry_sub_category_id",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200:
                print(f"Error fetching products: {response.text}")
                break
                
            batch = response.json()
            if not batch:
                break
                
            all_products.extend(batch)
            offset += limit
            
            if len(batch) < limit:
                break
        
        print(f"Fetched {len(all_products)} products")
        return all_products

    def normalize_name(self, name: str) -> str:
        """Normalize product name for comparison"""
        # Remove extra spaces
        name = ' '.join(name.split())
        
        # Apply equivalent term substitutions
        for old, new in self.equivalent_terms.items():
            name = name.replace(old, new)
        
        # Remove variation letters (Plus X, Pro Z, etc.)
        name = re.sub(r'\s+(Plus|Pro|Elite|Ultra|Max|Prime|Select|Premium)\s+[A-Z]\b', r' \1', name)
        
        # Fix double words
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        
        return name.strip()

    def calculate_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two product names"""
        # First try exact match after normalization
        norm1 = self.normalize_name(name1)
        norm2 = self.normalize_name(name2)
        
        if norm1 == norm2:
            return 1.0
        
        # Use SequenceMatcher for fuzzy matching
        return SequenceMatcher(None, norm1.lower(), norm2.lower()).ratio()

    def find_duplicate_groups(self, products: List[Dict]) -> List[List[Dict]]:
        """Group products that are likely duplicates"""
        print("\nAnalyzing for duplicates...")
        
        # Create groups of potential duplicates
        groups = []
        processed = set()
        
        for i, product1 in enumerate(products):
            if product1['id'] in processed:
                continue
                
            group = [product1]
            processed.add(product1['id'])
            
            for j, product2 in enumerate(products[i+1:], i+1):
                if product2['id'] in processed:
                    continue
                    
                similarity = self.calculate_similarity(product1['name'], product2['name'])
                
                # Consider duplicates if:
                # 1. Similarity > 0.85
                # 2. Same plant part and industry
                # 3. Created by similar agents
                if (similarity > 0.85 and 
                    product1['plant_part_id'] == product2['plant_part_id'] and
                    product1['industry_sub_category_id'] == product2['industry_sub_category_id']):
                    
                    group.append(product2)
                    processed.add(product2['id'])
            
            if len(group) > 1:
                groups.append(group)
        
        print(f"Found {len(groups)} duplicate groups")
        return groups

    def analyze_duplicate_patterns(self, groups: List[List[Dict]]) -> Dict:
        """Analyze patterns in duplicate groups"""
        patterns = {
            'total_duplicates': sum(len(g) - 1 for g in groups),
            'agents_involved': {},
            'pattern_counts': {},
            'examples': []
        }
        
        for group in groups[:10]:  # First 10 examples
            names = [p['name'] for p in group]
            agents = [p['source_agent'] for p in group]
            
            # Count agents
            for agent in agents:
                patterns['agents_involved'][agent] = patterns['agents_involved'].get(agent, 0) + 1
            
            # Identify pattern type
            for pattern_name, pattern_regex in self.duplicate_patterns.items():
                if any(re.search(pattern_regex, name) for name in names):
                    patterns['pattern_counts'][pattern_name] = patterns['pattern_counts'].get(pattern_name, 0) + 1
            
            patterns['examples'].append({
                'names': names,
                'ids': [p['id'] for p in group],
                'agents': list(set(agents))
            })
        
        return patterns

    def merge_duplicate_group(self, group: List[Dict]) -> Dict:
        """Determine which product to keep and which to remove"""
        # Sort by: 1) created date (older first), 2) name length (shorter preferred), 3) id
        sorted_group = sorted(group, key=lambda x: (
            x['created_at'],
            len(x['name']),
            x['id']
        ))
        
        keep = sorted_group[0]
        remove = sorted_group[1:]
        
        return {
            'keep': keep,
            'remove': remove,
            'reason': f"Keeping oldest/simplest: {keep['name']} (ID: {keep['id']})"
        }

    def delete_duplicates(self, duplicate_groups: List[List[Dict]], dry_run: bool = True):
        """Delete duplicate products"""
        merge_plan = []
        
        for group in duplicate_groups:
            merge_info = self.merge_duplicate_group(group)
            merge_plan.append(merge_info)
        
        if dry_run:
            print("\n=== DRY RUN - No changes will be made ===")
        
        total_to_delete = sum(len(m['remove']) for m in merge_plan)
        print(f"\nPlanning to delete {total_to_delete} duplicate products")
        
        # Show first 10 examples
        for i, merge in enumerate(merge_plan[:10]):
            print(f"\n{i+1}. Keep: {merge['keep']['name']} (ID: {merge['keep']['id']})")
            for dup in merge['remove']:
                print(f"   Delete: {dup['name']} (ID: {dup['id']})")
        
        if not dry_run and input("\nProceed with deletion? (yes/no): ").lower() == 'yes':
            deleted_count = 0
            for merge in merge_plan:
                for dup in merge['remove']:
                    response = requests.delete(
                        f"{self.supabase_url}/rest/v1/uses_products",
                        headers=self.headers,
                        params={"id": f"eq.{dup['id']}"}
                    )
                    if response.status_code in [200, 204]:
                        deleted_count += 1
                    else:
                        print(f"Failed to delete ID {dup['id']}: {response.text}")
            
            print(f"\nDeleted {deleted_count} duplicate products")

    def generate_report(self):
        """Generate comprehensive duplicate analysis report"""
        products = self.fetch_all_products()
        groups = self.find_duplicate_groups(products)
        patterns = self.analyze_duplicate_patterns(groups)
        
        print("\n" + "="*60)
        print("DUPLICATE ANALYSIS REPORT")
        print("="*60)
        
        print(f"\nTotal Products: {len(products)}")
        print(f"Duplicate Groups: {len(groups)}")
        print(f"Total Duplicates: {patterns['total_duplicates']}")
        
        print("\nAgents Creating Duplicates:")
        for agent, count in sorted(patterns['agents_involved'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {agent}: {count} duplicates")
        
        print("\nDuplicate Patterns Found:")
        for pattern, count in patterns['pattern_counts'].items():
            print(f"  {pattern}: {count} occurrences")
        
        print("\nExample Duplicate Groups:")
        for i, example in enumerate(patterns['examples'], 1):
            print(f"\n{i}. IDs: {example['ids']}")
            for name in example['names']:
                print(f"   - {name}")
            print(f"   Agents: {', '.join(example['agents'])}")
        
        return groups


def create_improved_duplicate_checker():
    """Create an improved duplicate detection function for agents"""
    code = '''
def check_duplicate_advanced(self, name: str, fuzzy_threshold: float = 0.85) -> bool:
    """Advanced duplicate detection using fuzzy matching"""
    
    # Normalize the name first
    normalized_name = self.normalize_product_name(name)
    
    # Check exact match first
    response = requests.get(
        f"{self.supabase_url}/rest/v1/uses_products",
        headers=self.headers,
        params={
            "name": f"eq.{name}",
            "select": "id",
            "limit": "1"
        }
    )
    
    if response.status_code == 200 and response.json():
        return True
    
    # Fuzzy match check - get products with similar names
    # Using PostgreSQL's similarity function
    query = f"""
    SELECT id, name, similarity(name, '{name}') as sim_score
    FROM uses_products
    WHERE similarity(name, '{name}') > {fuzzy_threshold}
    ORDER BY sim_score DESC
    LIMIT 5
    """
    
    # For API agents, we need to use a different approach
    # Get all products and check similarity client-side
    all_products_response = requests.get(
        f"{self.supabase_url}/rest/v1/uses_products",
        headers=self.headers,
        params={
            "select": "id,name",
            "order": "id.desc",
            "limit": "500"  # Check recent products
        }
    )
    
    if all_products_response.status_code == 200:
        products = all_products_response.json()
        for product in products:
            if self.calculate_name_similarity(normalized_name, product['name']) > fuzzy_threshold:
                print(f"Found similar product: {product['name']} (ID: {product['id']})")
                return True
    
    return False

def normalize_product_name(self, name: str) -> str:
    """Normalize product name to reduce duplicates"""
    # Remove extra spaces
    name = ' '.join(name.split())
    
    # Remove variation letters (Plus X, Pro Z, etc.)
    name = re.sub(r'\s+(Plus|Pro|Elite|Ultra|Max|Prime|Select|Premium)\s+[A-Z]\b', r' \1', name)
    
    # Fix double words
    name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
    
    # Standardize Hemp variations
    name = name.replace('Hemp-Based', 'Hemp')
    name = name.replace('Hemp-Derived', 'Hemp')
    
    return name.strip()

def calculate_name_similarity(self, name1: str, name2: str) -> float:
    """Calculate similarity between two names"""
    from difflib import SequenceMatcher
    norm1 = self.normalize_product_name(name1)
    norm2 = self.normalize_product_name(name2)
    return SequenceMatcher(None, norm1.lower(), norm2.lower()).ratio()
'''
    
    print("\n" + "="*60)
    print("IMPROVED DUPLICATE DETECTION CODE")
    print("="*60)
    print(code)
    
    with open('/home/<USER>/projects/HQz-Ai-DB-MCP-3/duplicate_detection_utils.py', 'w') as f:
        f.write(code)
    
    print("\nSaved to: duplicate_detection_utils.py")


if __name__ == "__main__":
    analyzer = DuplicateAnalyzer()
    
    print("Hemp Products Duplicate Analysis Tool")
    print("=====================================")
    print("1. Generate analysis report")
    print("2. Clean duplicates (dry run)")
    print("3. Clean duplicates (actual deletion)")
    print("4. Create improved detection code")
    
    choice = input("\nSelect option (1-4): ")
    
    if choice == "1":
        analyzer.generate_report()
    elif choice == "2":
        products = analyzer.fetch_all_products()
        groups = analyzer.find_duplicate_groups(products)
        analyzer.delete_duplicates(groups, dry_run=True)
    elif choice == "3":
        products = analyzer.fetch_all_products()
        groups = analyzer.find_duplicate_groups(products)
        analyzer.delete_duplicates(groups, dry_run=False)
    elif choice == "4":
        create_improved_duplicate_checker()
    else:
        print("Invalid choice")