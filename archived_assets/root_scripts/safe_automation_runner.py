#!/usr/bin/env python3
"""
Safe Automation Runner - Prevents all quality issues
- Uses enhanced quality control pipeline
- Runs comprehensive cleanup after each batch
- Monitors for issues in real-time
"""
import os
import sys
import subprocess
import time
import json
from datetime import datetime
from pathlib import Path
import psycopg2
from dotenv import load_dotenv

load_dotenv()

class SafeAutomationRunner:
    def __init__(self):
        self.conn = psycopg2.connect(os.getenv('DATABASE_URL'))
        self.cursor = self.conn.cursor()
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        self.log_file = self.log_dir / f"safe_automation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
    def log(self, message: str):
        """Log with timestamp"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_line = f"{timestamp} - {message}"
        print(log_line)
        with open(self.log_file, 'a') as f:
            f.write(log_line + '\n')
    
    def check_current_quality(self) -> dict:
        """Check current database quality metrics"""
        metrics = {}
        
        # Count products with formatting issues
        self.cursor.execute("""
            SELECT COUNT(*) FROM uses_products 
            WHERE description LIKE '%**%' 
               OR name LIKE '%**%'
               OR array_to_string(benefits_advantages, ' ') LIKE '%**%'
        """)
        metrics['formatting_issues'] = self.cursor.fetchone()[0]
        
        # Count potential duplicates
        self.cursor.execute("""
            SELECT COUNT(*) FROM (
                SELECT LOWER(name) as lower_name, COUNT(*) as cnt 
                FROM uses_products 
                GROUP BY LOWER(name) 
                HAVING COUNT(*) > 1
            ) as dups
        """)
        metrics['duplicate_groups'] = self.cursor.fetchone()[0]
        
        # Count low quality products
        self.cursor.execute("""
            SELECT COUNT(*) FROM uses_products 
            WHERE data_completeness_score < 70
               OR description IS NULL 
               OR LENGTH(description) < 100
               OR array_length(benefits_advantages, 1) < 3
        """)
        metrics['low_quality'] = self.cursor.fetchone()[0]
        
        # Count cannabis terminology
        self.cursor.execute("""
            SELECT COUNT(*) FROM uses_products 
            WHERE LOWER(name || ' ' || COALESCE(description, '')) ~ 
                  '(cannabis|marijuana|weed|ganja|thc|psychoactive)'
        """)
        metrics['cannabis_terms'] = self.cursor.fetchone()[0]
        
        return metrics
    
    def run_ai_discovery(self, num_products: int = 5) -> int:
        """Run AI discovery with enhanced quality control"""
        self.log(f"🤖 Starting AI discovery for {num_products} products...")
        
        # Create temporary script that uses enhanced pipeline
        temp_script = """
import sys
sys.path.append('.')
from enhanced_quality_control_pipeline import EnhancedQualityControlPipeline
from working_product_adder_v2 import EnhancedProductAdder
import psycopg2
from dotenv import load_dotenv
import os

load_dotenv()
conn = psycopg2.connect(os.getenv('DATABASE_URL'))

# Override the pipeline in the product adder
class SafeProductAdder(EnhancedProductAdder):
    def __init__(self):
        super().__init__()
        self.pipeline = EnhancedQualityControlPipeline(self.conn)

adder = SafeProductAdder()
products_added = adder.generate_products(count={num_products})
adder.close()

print(f"Added: {{products_added}} new products")
""".format(num_products=num_products)
        
        with open('temp_safe_adder.py', 'w') as f:
            f.write(temp_script)
        
        try:
            result = subprocess.run(
                [sys.executable, 'temp_safe_adder.py'],
                capture_output=True,
                text=True,
                timeout=300
            )
            
            # Log output for debugging
            if result.stderr:
                self.log(f"❌ Script error: {result.stderr}")
            if result.stdout:
                self.log(f"Script output: {result.stdout[:200]}...")
                
            # Parse results
            added = 0
            if result.stdout:
                for line in result.stdout.split('\n'):
                    if 'Added:' in line and 'new products' in line:
                        try:
                            added = int(line.split()[1])
                        except:
                            pass
            
            self.log(f"✅ AI Discovery completed: {added} products added")
            return added
            
        except Exception as e:
            self.log(f"❌ AI Discovery error: {e}")
            return 0
        finally:
            if os.path.exists('temp_safe_adder.py'):
                os.remove('temp_safe_adder.py')
    
    def run_cleanup(self) -> dict:
        """Run comprehensive cleanup"""
        self.log("🧹 Running comprehensive cleanup...")
        
        cleanup_results = {
            'formatting_fixed': 0,
            'duplicates_removed': 0,
            'cannabis_terms_fixed': 0
        }
        
        try:
            # Fix formatting issues
            self.cursor.execute("""
                UPDATE uses_products
                SET name = regexp_replace(name, '\\*\\*([^*]+)\\*\\*', '\\1', 'g'),
                    description = regexp_replace(description, '\\*\\*([^*]+)\\*\\*', '\\1', 'g')
                WHERE name LIKE '%**%' OR description LIKE '%**%'
            """)
            cleanup_results['formatting_fixed'] = self.cursor.rowcount
            
            # Fix benefits formatting
            self.cursor.execute("""
                UPDATE uses_products
                SET benefits_advantages = ARRAY(
                    SELECT regexp_replace(benefit, '\\*\\*([^*]+)\\*\\*', '\\1', 'g')
                    FROM unnest(benefits_advantages) AS benefit
                )
                WHERE array_to_string(benefits_advantages, ' ') LIKE '%**%'
            """)
            cleanup_results['formatting_fixed'] += self.cursor.rowcount
            
            # Remove exact duplicates (keep the one with highest score)
            self.cursor.execute("""
                DELETE FROM uses_products a
                USING uses_products b
                WHERE a.id > b.id 
                  AND LOWER(TRIM(a.name)) = LOWER(TRIM(b.name))
                  AND a.plant_part_id = b.plant_part_id
                  AND (a.data_completeness_score < b.data_completeness_score 
                       OR (a.data_completeness_score = b.data_completeness_score AND a.id > b.id))
            """)
            cleanup_results['duplicates_removed'] = self.cursor.rowcount
            
            # Fix cannabis terminology
            self.cursor.execute("""
                UPDATE uses_products
                SET name = regexp_replace(name, '\\mcannabis\\M', 'industrial hemp', 'gi'),
                    description = regexp_replace(description, '\\mcannabis\\M', 'industrial hemp', 'gi')
                WHERE LOWER(name || ' ' || COALESCE(description, '')) ~ 'cannabis'
            """)
            cleanup_results['cannabis_terms_fixed'] = self.cursor.rowcount
            
            self.conn.commit()
            self.log(f"✅ Cleanup completed: {cleanup_results}")
            
        except Exception as e:
            self.conn.rollback()
            self.log(f"❌ Cleanup error: {e}")
            
        return cleanup_results
    
    def run_quality_check(self) -> bool:
        """Run quality check and alert on issues"""
        self.log("🔍 Running quality check...")
        
        metrics = self.check_current_quality()
        
        issues = []
        if metrics['formatting_issues'] > 0:
            issues.append(f"{metrics['formatting_issues']} formatting issues")
        if metrics['duplicate_groups'] > 0:
            issues.append(f"{metrics['duplicate_groups']} duplicate groups")
        if metrics['low_quality'] > 50:
            issues.append(f"{metrics['low_quality']} low quality products")
        if metrics['cannabis_terms'] > 0:
            issues.append(f"{metrics['cannabis_terms']} products with cannabis terms")
        
        if issues:
            self.log(f"⚠️  Quality issues found: {', '.join(issues)}")
            return False
        else:
            self.log("✅ All quality checks passed!")
            return True
    
    def run_cycle(self):
        """Run one complete automation cycle"""
        self.log("\n" + "="*60)
        self.log("🔄 STARTING AUTOMATION CYCLE")
        self.log("="*60)
        
        # 1. Initial quality check
        self.log("\n📊 Initial Quality Metrics:")
        initial_metrics = self.check_current_quality()
        for key, value in initial_metrics.items():
            self.log(f"  {key}: {value}")
        
        # 2. Run AI discovery
        products_added = self.run_ai_discovery(num_products=5)
        
        # 3. Run cleanup immediately
        if products_added > 0:
            time.sleep(2)  # Brief pause
            cleanup_results = self.run_cleanup()
        
        # 4. Final quality check
        self.log("\n📊 Final Quality Metrics:")
        final_metrics = self.check_current_quality()
        for key, value in final_metrics.items():
            self.log(f"  {key}: {value}")
        
        # 5. Generate summary
        self.log("\n📈 Cycle Summary:")
        self.log(f"  Products added: {products_added}")
        self.log(f"  Quality improved: {initial_metrics['formatting_issues'] - final_metrics['formatting_issues']} formatting fixes")
        self.log(f"  Duplicates removed: {initial_metrics['duplicate_groups'] - final_metrics['duplicate_groups']}")
        
        return products_added > 0 and self.run_quality_check()
    
    def run_continuous(self, interval_minutes: int = 30):
        """Run continuous automation with quality monitoring"""
        self.log(f"🚀 Starting continuous automation (every {interval_minutes} minutes)")
        
        cycle_count = 0
        total_added = 0
        
        while True:
            cycle_count += 1
            self.log(f"\n🔢 Cycle #{cycle_count}")
            
            try:
                success = self.run_cycle()
                
                if not success:
                    self.log("⚠️  Cycle completed with warnings")
                
                # Save metrics
                metrics = self.check_current_quality()
                metrics['timestamp'] = datetime.now().isoformat()
                metrics['cycle'] = cycle_count
                
                with open('automation_metrics.json', 'w') as f:
                    json.dump(metrics, f, indent=2)
                
            except Exception as e:
                self.log(f"❌ Cycle error: {e}")
            
            # Wait for next cycle
            self.log(f"💤 Waiting {interval_minutes} minutes until next cycle...")
            time.sleep(interval_minutes * 60)


if __name__ == "__main__":
    runner = SafeAutomationRunner()
    
    # Run single cycle for testing
    print("\n🧪 Running single test cycle...")
    runner.run_cycle()
    
    # Uncomment to run continuous
    # runner.run_continuous(interval_minutes=30)