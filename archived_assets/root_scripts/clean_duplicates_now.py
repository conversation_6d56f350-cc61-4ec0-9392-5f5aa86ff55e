#!/usr/bin/env python3
"""
Clean up duplicates in the database
Focus on the most problematic patterns first
"""

import requests
import json
from datetime import datetime

# Service role key for authentication
service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"

headers = {
    "apikey": service_role_key,
    "Authorization": f"Bearer {service_role_key}",
    "Content-Type": "application/json"
}

def find_and_remove_obvious_duplicates():
    """Remove the most obvious duplicates"""
    
    print("Finding obvious duplicates...")
    
    # Pattern 1: Products with double words (e.g., "Cannabinoid Cannabinoid")
    patterns_to_fix = [
        ('Cannabinoid Cannabinoid', 'Cannabinoid'),
        ('Formulation Formulation', 'Formulation'),
        ('Textile Textile', 'Textile'),
        ('Plastic Plastic', 'Plastic'),
        ('Based Based', 'Based'),
        ('Product Product', 'Product'),
        ('Material Material', 'Material'),
        ('Biodegradable Biodegradable', 'Biodegradable'),
        ('Processing Processing', 'Processing'),
        ('Fiber Fiber', 'Fiber'),
    ]
    
    total_deleted = 0
    
    for bad_pattern, good_pattern in patterns_to_fix:
        # Find products with the bad pattern
        response = requests.get(
            f"{supabase_url}/rest/v1/uses_products",
            headers=headers,
            params={
                "select": "id,name,created_at",
                "name": f"ilike.*{bad_pattern}*",
                "order": "created_at.desc"
            }
        )
        
        if response.status_code == 200:
            products = response.json()
            print(f"\nFound {len(products)} products with '{bad_pattern}'")
            
            # Keep the oldest one, delete the rest
            if len(products) > 1:
                # Sort by created_at to keep the oldest
                products.sort(key=lambda x: x['created_at'])
                to_delete = products[1:]  # All except the first
                
                for product in to_delete[:5]:  # Show first 5
                    print(f"  Will delete: {product['name']} (ID: {product['id']})")
                
                if len(to_delete) > 5:
                    print(f"  ... and {len(to_delete) - 5} more")
                
                # Delete them
                for product in to_delete:
                    del_response = requests.delete(
                        f"{supabase_url}/rest/v1/uses_products",
                        headers=headers,
                        params={"id": f"eq.{product['id']}"}
                    )
                    if del_response.status_code in [200, 204]:
                        total_deleted += 1
    
    print(f"\nDeleted {total_deleted} obvious duplicates")
    
    # Pattern 2: Products with variation letters (Plus X, Pro Z, etc.)
    print("\nFinding products with variation letters...")
    
    variation_patterns = ['Plus ', 'Pro ', 'Elite ', 'Ultra ', 'Max ', 'Prime ', 'Select ', 'Premium ']
    
    for pattern in variation_patterns:
        # Get products with this pattern followed by a single letter
        response = requests.get(
            f"{supabase_url}/rest/v1/uses_products",
            headers=headers,
            params={
                "select": "id,name,created_at",
                "or": f"(name.ilike.*{pattern}X *,name.ilike.*{pattern}Z *,name.ilike.*{pattern}Q *,name.ilike.*{pattern}V *,name.ilike.*{pattern}Neo *)",
                "order": "name"
            }
        )
        
        if response.status_code == 200 and response.json():
            products = response.json()
            print(f"\nFound {len(products)} products with '{pattern}[Letter]' pattern")
            
            # Group by base name (without the letter)
            groups = {}
            for product in products:
                # Remove the letter variation
                base_name = product['name']
                for letter in ['X', 'Z', 'Q', 'V', 'Neo']:
                    base_name = base_name.replace(f'{pattern}{letter} ', f'{pattern}')
                
                if base_name not in groups:
                    groups[base_name] = []
                groups[base_name].append(product)
            
            # Delete duplicates within each group
            for base_name, group in groups.items():
                if len(group) > 1:
                    # Keep the one without letter if exists, otherwise keep first
                    group.sort(key=lambda x: (pattern in x['name'] and not any(f'{pattern}{l}' in x['name'] for l in ['X', 'Z', 'Q', 'V', 'Neo']), x['created_at']))
                    to_delete = group[1:]
                    
                    for product in to_delete:
                        del_response = requests.delete(
                            f"{supabase_url}/rest/v1/uses_products",
                            headers=headers,
                            params={"id": f"eq.{product['id']}"}
                        )
                        if del_response.status_code in [200, 204]:
                            total_deleted += 1
                            print(f"  Deleted: {product['name']} (ID: {product['id']})")
    
    print(f"\nTotal deleted: {total_deleted} duplicates")
    return total_deleted


def update_agent_to_v2():
    """Update the main agent to use V2"""
    print("\nUpdating patent mining agent to V2...")
    
    # Copy V2 over the original
    import shutil
    src = "/home/<USER>/projects/HQz-Ai-DB-MCP-3/src/agents/specialized/patent_mining_agent_api_v2.py"
    dst = "/home/<USER>/projects/HQz-Ai-DB-MCP-3/src/agents/specialized/patent_mining_agent_api.py"
    
    try:
        shutil.copy(src, dst)
        print("✅ Updated patent mining agent to V2 with enhanced duplicate prevention")
    except Exception as e:
        print(f"❌ Failed to update agent: {e}")


if __name__ == "__main__":
    print("Hemp Database Duplicate Cleanup")
    print("==============================")
    
    # Clean duplicates
    deleted = find_and_remove_obvious_duplicates()
    
    # Update agent
    update_agent_to_v2()
    
    print(f"\n✅ Cleanup complete! Removed {deleted} duplicates")
    print("✅ Agent updated to prevent future duplicates")