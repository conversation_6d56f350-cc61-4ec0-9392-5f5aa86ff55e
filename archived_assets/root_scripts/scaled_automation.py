#!/usr/bin/env python3
"""
Scaled Automation System - Adds 50+ products per day
Runs every 30 minutes, adding 2-3 products per run
"""
import os
import sys
import time
import subprocess
from datetime import datetime
import schedule
import logging
from pathlib import Path

# Setup logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/scaled_automation_{datetime.now().strftime("%Y%m%d")}.log')
    ]
)
logger = logging.getLogger(__name__)


class ScaledAutomation:
    def __init__(self):
        self.products_per_run = 3  # 3 products every 30 mins = 144/day
        self.run_count = 0
        self.total_added_today = 0
        self.start_time = datetime.now()
        
    def run_product_addition(self):
        """Run product addition with the quality-controlled script"""
        logger.info(f"🤖 Starting product addition (Run #{self.run_count + 1})")
        
        try:
            # Use the enhanced product adder with quality control
            result = subprocess.run(
                [sys.executable, "working_product_adder_v2.py"],
                capture_output=True,
                text=True,
                timeout=180
            )
            
            # Parse results
            added = 0
            if result.stdout:
                for line in result.stdout.split('\n'):
                    if 'Added:' in line and 'new products' in line:
                        # Extract number from "Added: X new products"
                        parts = line.split()
                        for i, part in enumerate(parts):
                            if part.isdigit() and i > 0 and parts[i-1] == 'Added:':
                                added = int(part)
                                break
            
            self.total_added_today += added
            self.run_count += 1
            
            logger.info(f"✅ Added {added} products (Total today: {self.total_added_today})")
            return added
            
        except subprocess.TimeoutExpired:
            logger.error("❌ Product addition timed out")
            return 0
        except Exception as e:
            logger.error(f"❌ Error: {e}")
            return 0
    
    def run_enrichment(self):
        """Run company matching enrichment"""
        logger.info("🏢 Running company enrichment...")
        
        try:
            # Simple company matching using the basic matcher
            result = subprocess.run(
                ["python", "company_matcher.py"],
                capture_output=True,
                text=True,
                timeout=60
            )
            logger.info("✅ Company enrichment completed")
        except:
            logger.warning("⚠️  Company enrichment skipped")
    
    def run_cleanup(self):
        """Run deduplication and cleanup"""
        logger.info("🧹 Running cleanup...")
        
        try:
            result = subprocess.run(
                ["python", "merge_exact_duplicates.py"],
                capture_output=True,
                text=True,
                timeout=30
            )
            logger.info("✅ Cleanup completed")
        except:
            logger.warning("⚠️  Cleanup skipped")
    
    def generate_stats(self):
        """Generate current statistics"""
        try:
            import psycopg2
            from dotenv import load_dotenv
            
            load_dotenv()
            conn = psycopg2.connect(os.getenv('DATABASE_URL'))
            cursor = conn.cursor()
            
            # Get current stats
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(*) FILTER (WHERE created_at > CURRENT_DATE) as today,
                    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '1 hour') as last_hour
                FROM uses_products
            """)
            
            total, today, last_hour = cursor.fetchone()
            
            # Products per minute rate
            runtime = (datetime.now() - self.start_time).total_seconds() / 60
            rate = self.total_added_today / runtime if runtime > 0 else 0
            
            logger.info(f"\n📊 AUTOMATION STATS")
            logger.info(f"  Total Products: {total}")
            logger.info(f"  Added Today: {today}")
            logger.info(f"  Added Last Hour: {last_hour}")
            logger.info(f"  Current Rate: {rate:.2f} products/minute")
            logger.info(f"  Projected Daily: {int(rate * 60 * 24)} products")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"Stats error: {e}")
    
    def run_cycle(self):
        """Run one complete automation cycle"""
        logger.info("\n" + "="*60)
        logger.info(f"🔄 AUTOMATION CYCLE {self.run_count + 1}")
        logger.info("="*60)
        
        start = time.time()
        
        # 1. Add products
        added = self.run_product_addition()
        
        # 2. Enrich with companies (every 5th run)
        if self.run_count % 5 == 0:
            self.run_enrichment()
        
        # 3. Cleanup (every 10th run)
        if self.run_count % 10 == 0:
            self.run_cleanup()
        
        # 4. Generate stats
        self.generate_stats()
        
        duration = time.time() - start
        logger.info(f"\n⏱️  Cycle completed in {duration:.1f} seconds")
        logger.info("="*60)
    
    def run_continuous(self):
        """Run continuously with scheduled intervals"""
        logger.info("🚀 SCALED AUTOMATION SYSTEM STARTED")
        logger.info(f"📅 {datetime.now()}")
        logger.info(f"⚙️  Configuration:")
        logger.info(f"  - Products per run: {self.products_per_run}")
        logger.info(f"  - Run interval: 30 minutes")
        logger.info(f"  - Target: 50+ products/day")
        
        # Schedule runs every 30 minutes
        schedule.every(30).minutes.do(self.run_cycle)
        
        # Run immediately
        self.run_cycle()
        
        # Keep running
        logger.info("\n⏰ Next run in 30 minutes...")
        logger.info("Press Ctrl+C to stop\n")
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute


def main():
    """Main entry point"""
    automation = ScaledAutomation()
    
    if "--once" in sys.argv:
        # Single run mode
        automation.run_cycle()
    else:
        # Continuous mode
        automation.run_continuous()


if __name__ == "__main__":
    main()