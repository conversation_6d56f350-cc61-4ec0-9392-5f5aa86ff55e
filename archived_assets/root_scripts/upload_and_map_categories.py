#!/usr/bin/env python3
"""
Upload category images to Supabase and map to products
"""

import os
import json
from supabase import create_client, Client

# Get Supabase credentials
SUPABASE_URL = os.getenv('VITE_SUPABASE_URL', 'https://ktoqznqmlnxrtvubewyz.supabase.co')
SUPABASE_SERVICE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

def upload_to_supabase():
    """Upload images to Supabase storage"""
    
    if not SUPABASE_SERVICE_KEY:
        print("❌ SUPABASE_SERVICE_ROLE_KEY not found")
        return None
        
    # Initialize Supabase client
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
    
    # Load results
    with open('generated_category_images/results_free.json', 'r') as f:
        results = json.load(f)
    
    uploaded = []
    
    print("📤 Uploading to Supabase Storage...")
    
    for item in results:
        filepath = item['filepath']
        
        # Read file
        with open(filepath, 'rb') as f:
            file_data = f.read()
        
        # Upload to Supabase
        storage_path = f"product-images/categories/{item['filename']}"
        
        try:
            # Upload file
            response = supabase.storage.from_('product-images').upload(
                storage_path,
                file_data,
                file_options={"content-type": "image/jpeg"}
            )
            
            # Get public URL
            public_url = supabase.storage.from_('product-images').get_public_url(storage_path)
            
            print(f"✅ Uploaded: {item['subcategory']}")
            
            uploaded.append({
                **item,
                'storage_path': storage_path,
                'public_url': public_url
            })
            
        except Exception as e:
            print(f"❌ Failed to upload {item['filename']}: {e}")
    
    return uploaded

def map_products_to_categories(uploaded_images):
    """Generate SQL to map products to category images"""
    
    print("\n📊 Generating product mappings...")
    
    # Category mapping rules
    mapping_rules = {
        'hemp_seeds': ['seed', 'hearts', 'hulled'],
        'hemp_oil_culinary': ['oil', 'cooking', 'culinary'],
        'hemp_protein': ['protein', 'powder', 'supplement'],
        'hemp_beverages': ['tea', 'coffee', 'drink', 'beverage', 'milk'],
        'cbd_oils': ['cbd', 'cannabinoid', 'tincture', 'extract'],
        'topicals': ['balm', 'cream', 'lotion', 'salve', 'ointment'],
        'capsules': ['capsule', 'pill', 'tablet', 'softgel'],
        'raw_fiber': ['fiber', 'fibre', 'raw hemp'],
        'fabric': ['fabric', 'textile', 'cloth', 'material'],
        'clothing': ['clothing', 'apparel', 'shirt', 'pants', 'dress'],
        'hempcrete': ['hempcrete', 'concrete', 'building block'],
        'insulation': ['insulation', 'insulating'],
        'panels': ['panel', 'board', 'sheet'],
        'skincare': ['skincare', 'serum', 'moisturizer', 'face'],
        'soap': ['soap', 'bar', 'wash'],
        'haircare': ['shampoo', 'conditioner', 'hair'],
        'bioplastic': ['plastic', 'bioplastic', 'polymer'],
        'composites': ['composite', 'material', 'compound'],
        'packaging': ['packaging', 'container', 'package']
    }
    
    # Generate SQL queries
    sql_queries = []
    
    for image in uploaded_images:
        subcategory = image['subcategory']
        url = image['public_url']
        
        if subcategory in mapping_rules:
            keywords = mapping_rules[subcategory]
            conditions = " OR ".join([
                f"LOWER(name) LIKE '%{kw}%' OR LOWER(description) LIKE '%{kw}%'"
                for kw in keywords
            ])
            
            sql_queries.append(f"""
-- Map {subcategory} products
UPDATE uses_products
SET image_url = '{url}'
WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
  AND ({conditions})
  AND NOT EXISTS (
    SELECT 1 FROM uses_products p2 
    WHERE p2.id = uses_products.id 
    AND p2.image_url LIKE '%/categories/%'
  );
""")
    
    # Save SQL
    with open('apply_category_mappings.sql', 'w') as f:
        f.write("-- Apply Category Image Mappings\n")
        f.write("-- This will map category images to products without images\n\n")
        
        for query in sql_queries:
            f.write(query + "\n")
        
        # Add verification query
        f.write("""
-- Verify the results
SELECT 
    COUNT(*) FILTER (WHERE image_url IS NOT NULL AND image_url != '') as with_images,
    COUNT(*) FILTER (WHERE image_url IS NULL OR image_url = '') as without_images,
    COUNT(*) FILTER (WHERE image_url LIKE '%/categories/%') as category_images,
    COUNT(*) as total
FROM uses_products;
""")
    
    print(f"✅ SQL queries saved to: apply_category_mappings.sql")
    
    return sql_queries

def main():
    print("🚀 CATEGORY IMAGE UPLOAD & MAPPING")
    print("=" * 60)
    
    # Check if images exist
    if not os.path.exists('generated_category_images/results_free.json'):
        print("❌ No images found. Run generate_category_images_free.py first")
        return
    
    # Option 1: Upload to Supabase
    print("\n1️⃣ Upload to Supabase Storage")
    uploaded = upload_to_supabase()
    
    if uploaded:
        print(f"\n✅ Uploaded {len(uploaded)} images to Supabase")
        
        # Generate mappings
        print("\n2️⃣ Generate Product Mappings")
        sql_queries = map_products_to_categories(uploaded)
        
        print(f"\n📊 Impact Summary:")
        print(f"• {len(uploaded)} category images created")
        print(f"• Potential to cover 4,000+ products")
        print(f"• Total cost: $0.00")
        
        print("\n🎯 Next Steps:")
        print("1. Review apply_category_mappings.sql")
        print("2. Run the SQL in Supabase to map products")
        print("3. Check the quality dashboard for coverage")
    else:
        print("\n⚠️  Manual upload needed:")
        print("1. Go to Supabase dashboard > Storage")
        print("2. Create bucket 'product-images' if needed")
        print("3. Upload files from generated_category_images/")
        print("4. Run apply_category_mappings.sql")

if __name__ == "__main__":
    try:
        import supabase
        main()
    except ImportError:
        print("❌ Supabase library not installed")
        print("Run: pip install supabase")
        
        # Generate SQL anyway
        print("\n📝 Generating SQL mappings without upload...")
        with open('generated_category_images/results_free.json', 'r') as f:
            results = json.load(f)
        
        # Add fake URLs for manual replacement
        for r in results:
            r['public_url'] = f"https://your-bucket.supabase.co/storage/v1/object/public/product-images/categories/{r['filename']}"
        
        map_products_to_categories(results)
        print("\n⚠️  Remember to update the URLs in the SQL after uploading!")