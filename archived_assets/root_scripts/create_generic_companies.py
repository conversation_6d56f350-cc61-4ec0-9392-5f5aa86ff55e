#!/usr/bin/env python3
"""
Create generic companies for unmatched products based on categories
"""
import os
import psycopg2
from dotenv import load_dotenv
from datetime import datetime

load_dotenv()

def main():
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    print("🏢 CREATING GENERIC COMPANIES FOR UNMATCHED PRODUCTS")
    print("=" * 60)
    
    # Define generic companies by product category
    generic_companies = [
        {
            'name': 'Hemp Fiber Industries',
            'description': 'Generic manufacturer of hemp fiber products including textiles, composites, and industrial materials.',
            'type': 'Manufacturer',
            'keywords': ['fiber', 'textile', 'fabric', 'composite', 'rope', 'twine', 'thread']
        },
        {
            'name': 'Hemp Food Co.',
            'description': 'Generic producer of hemp-based food products including seeds, oil, and protein.',
            'type': 'Food Producer',
            'keywords': ['seed', 'oil', 'protein', 'food', 'milk', 'flour', 'butter', 'hearts']
        },
        {
            'name': 'Hemp Building Materials Ltd.',
            'description': 'Generic supplier of hemp construction materials including hempcrete and insulation.',
            'type': 'Building Supplier',
            'keywords': ['hempcrete', 'building', 'construction', 'insulation', 'concrete', 'brick', 'block']
        },
        {
            'name': 'Hemp Wellness Products',
            'description': 'Generic manufacturer of hemp-based wellness and personal care products.',
            'type': 'Wellness Brand',
            'keywords': ['cbd', 'cbg', 'tincture', 'cream', 'balm', 'lotion', 'skincare', 'wellness']
        },
        {
            'name': 'Hemp Paper Industries',
            'description': 'Generic manufacturer of hemp paper products.',
            'type': 'Paper Manufacturer',
            'keywords': ['paper', 'pulp', 'cardboard', 'packaging']
        },
        {
            'name': 'Hemp Plastics International',
            'description': 'Generic manufacturer of hemp-based plastic alternatives and biocomposites.',
            'type': 'Plastics Manufacturer',
            'keywords': ['plastic', 'bioplastic', 'polymer', 'resin']
        },
        {
            'name': 'Hemp Agricultural Supplies',
            'description': 'Generic supplier of hemp agricultural products and supplies.',
            'type': 'Agricultural Supplier',
            'keywords': ['hurd', 'bedding', 'mulch', 'fertilizer', 'soil', 'amendment']
        }
    ]
    
    # Create companies
    company_ids = {}
    for company_data in generic_companies:
        # Check if company exists
        cursor.execute("""
            SELECT id FROM hemp_companies 
            WHERE name = %s
        """, (company_data['name'],))
        
        result = cursor.fetchone()
        if result:
            company_ids[company_data['name']] = result[0]
            print(f"✓ Found existing: {company_data['name']}")
        else:
            # Create new company
            cursor.execute("""
                INSERT INTO hemp_companies (name, description, company_type, verified, created_at)
                VALUES (%s, %s, %s, true, NOW())
                RETURNING id
            """, (company_data['name'], company_data['description'], company_data['type']))
            
            company_ids[company_data['name']] = cursor.fetchone()[0]
            conn.commit()
            print(f"✅ Created: {company_data['name']}")
    
    # Match products without companies
    cursor.execute("""
        SELECT p.id, p.name, p.description
        FROM uses_products p
        WHERE NOT EXISTS (
            SELECT 1 FROM hemp_company_products hcp
            WHERE hcp.product_id = p.id
        )
    """)
    
    unmatched_products = cursor.fetchall()
    print(f"\n📦 Found {len(unmatched_products)} products without companies")
    
    matches_made = 0
    for product_id, product_name, product_desc in unmatched_products:
        product_text = (product_name + ' ' + (product_desc or '')).lower()
        
        # Try to match with generic companies
        for company_data in generic_companies:
            # Check if any keyword matches
            if any(keyword in product_text for keyword in company_data['keywords']):
                company_id = company_ids[company_data['name']]
                
                # Create relationship
                try:
                    cursor.execute("""
                        INSERT INTO hemp_company_products 
                        (company_id, product_id, relationship_type, is_primary, created_at)
                        VALUES (%s, %s, 'manufactures', true, NOW())
                        ON CONFLICT DO NOTHING
                    """, (company_id, product_id))
                    
                    if cursor.rowcount > 0:
                        matches_made += 1
                        print(f"  ✓ Matched '{product_name[:50]}...' to {company_data['name']}")
                    
                    break  # Only match to first matching company
                    
                except Exception as e:
                    print(f"  ❌ Error matching product {product_id}: {e}")
    
    conn.commit()
    
    # Show final statistics
    cursor.execute("""
        SELECT 
            COUNT(*) as total,
            COUNT(DISTINCT hcp.product_id) as with_companies
        FROM uses_products p
        LEFT JOIN hemp_company_products hcp ON p.id = hcp.product_id
    """)
    
    total, with_companies = cursor.fetchone()
    
    print(f"\n📊 FINAL STATISTICS")
    print("=" * 60)
    print(f"Total products: {total}")
    print(f"Products with companies: {with_companies} ({with_companies/total*100:.1f}%)")
    print(f"Products without companies: {total - with_companies}")
    print(f"Matches made this run: {matches_made}")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    main()