#!/usr/bin/env python3
"""
More unique hemp products - second batch
"""
import os
import sys
import psycopg2
import random
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Second batch of unique products
MORE_UNIQUE_PRODUCTS = [
    # Space & Aerospace
    ("Hemp Space Suit Liner", "Radiation-resistant inner layer for space suits using hemp fiber", "fiber", "textiles"),
    ("Hemp Satellite Insulation", "Lightweight thermal protection for satellites", "fiber", "aerospace"),
    ("Hemp Mars Habitat Panels", "Building panels for extraterrestrial habitats", "fiber", "construction"),
    ("Hemp Zero-G Food Bars", "Space-optimized nutrition bars with hemp protein", "seed", "food"),
    ("Hemp Spacecraft Air Filter", "Advanced air purification for space vehicles", "fiber", "aerospace"),
    
    # Ocean & Marine
    ("Hemp Ocean Cleanup Net", "Biodegradable nets for ocean plastic collection", "fiber", "environmental"),
    ("Hemp Coral Reef Substrate", "Artificial reef structure from hemp composites", "fiber", "marine"),
    ("Hemp Submarine Cable Insulation", "Eco-friendly undersea cable protection", "fiber", "energy"),
    ("Hemp Marine Paint", "Anti-fouling boat paint with hemp compounds", "flower", "marine"),
    ("Hemp Desalination Membrane", "Salt water purification using hemp fibers", "fiber", "water"),
    
    # Sports & Performance
    ("Hemp Athletic Tape", "Kinesiology tape infused with hemp compounds", "fiber", "sports"),
    ("Hemp Golf Tees", "Biodegradable golf tees from compressed hemp", "hurd", "sports"),
    ("Hemp Ski Wax", "Performance ski wax with hemp-based compounds", "flower", "sports"),
    ("Hemp Yoga Mat", "Anti-microbial yoga mat with hemp fiber core", "fiber", "sports"),
    ("Hemp Running Gel", "Energy gel with hemp protein and MCT oil", "seed", "sports"),
    
    # Pet Products
    ("Hemp Pet Anxiety Vest", "Calming compression vest with hemp fiber", "fiber", "pets"),
    ("Hemp Cat Scratch Post", "Durable scratching surface from hemp rope", "fiber", "pets"),
    ("Hemp Dog Dental Chews", "Teeth cleaning chews with hemp fiber", "fiber", "pets"),
    ("Hemp Bird Cage Liner", "Absorbent cage liner from hemp paper", "fiber", "pets"),
    ("Hemp Fish Tank Filter", "Aquarium filtration media from hemp", "fiber", "pets"),
    
    # Art & Creative
    ("Hemp Artist Canvas", "Premium painting canvas from hemp fabric", "fiber", "art"),
    ("Hemp 3D Printer Filament", "Biodegradable 3D printing material", "fiber", "technology"),
    ("Hemp Sculpting Clay", "Moldable art medium with hemp fibers", "fiber", "art"),
    ("Hemp Photo Paper", "Archival quality photo paper from hemp", "fiber", "art"),
    ("Hemp Musical Instrument Parts", "Sustainable guitar picks and drum sticks", "fiber", "music"),
    
    # Emergency & Survival
    ("Hemp Emergency Shelter", "Rapid-deploy shelter from hemp composites", "fiber", "emergency"),
    ("Hemp Water Purification Straw", "Personal water filter with hemp carbon", "fiber", "survival"),
    ("Hemp Fire Blanket", "Fire-resistant emergency blanket", "fiber", "safety"),
    ("Hemp Survival Rope", "Multi-purpose cordage for emergencies", "fiber", "survival"),
    ("Hemp First Aid Bandages", "Antimicrobial wound dressings", "fiber", "medical"),
    
    # Baby & Child
    ("Hemp Baby Bottle", "BPA-free bottles from hemp bioplastic", "hurd", "baby"),
    ("Hemp Teething Ring", "Safe teething toy from hemp rubber", "fiber", "baby"),
    ("Hemp Diaper Cream", "Gentle rash cream with hemp oil", "seed", "baby"),
    ("Hemp Baby Food Pouches", "Reusable food pouches from hemp", "fiber", "baby"),
    ("Hemp Crib Mattress", "Organic breathable baby mattress", "fiber", "baby"),
    
    # Garden & Outdoor
    ("Hemp Garden Hose", "Flexible kink-free hose from hemp", "fiber", "garden"),
    ("Hemp Plant Stakes", "Biodegradable plant supports", "fiber", "garden"),
    ("Hemp Lawn Seed Mat", "Pre-seeded grass growing mat", "fiber", "garden"),
    ("Hemp Compost Accelerator", "Speed up composting with hemp enzymes", "leaves", "garden"),
    ("Hemp Deer Repellent", "Natural animal deterrent spray", "flower", "garden"),
    
    # Office & School
    ("Hemp Laptop Case", "Protective computer case from hemp", "fiber", "office"),
    ("Hemp Pencil Case", "Durable school supply organizer", "fiber", "school"),
    ("Hemp Whiteboard Eraser", "Reusable eraser with hemp fibers", "fiber", "office"),
    ("Hemp Binder Covers", "Customizable notebook covers", "fiber", "school"),
    ("Hemp Desk Organizer", "Modular desktop storage system", "hurd", "office"),
    
    # Specialty Foods
    ("Hemp Molecular Ice Cream", "Frozen dessert with hemp milk base", "seed", "food"),
    ("Hemp Fermented Cheese", "Aged vegan cheese from hemp", "seed", "food"),
    ("Hemp Sourdough Starter", "Probiotic bread starter with hemp", "seed", "food"),
    ("Hemp Caviar Alternative", "Luxury food spheres from hemp", "seed", "food"),
    ("Hemp Wagyu Alternative", "Premium meat substitute from hemp", "seed", "food")
]

def add_more_unique_products():
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    # Get plant part mapping
    plant_parts = {}
    cursor.execute("SELECT id, LOWER(name) FROM plant_parts")
    for pid, pname in cursor.fetchall():
        if 'fiber' in pname or 'bast' in pname:
            plant_parts['fiber'] = pid
        elif 'seed' in pname:
            plant_parts['seed'] = pid
        elif 'flower' in pname:
            plant_parts['flower'] = pid
        elif 'hurd' in pname or 'shiv' in pname:
            plant_parts['hurd'] = pid
        elif 'root' in pname:
            plant_parts['roots'] = pid
        elif 'leaves' in pname or 'leaf' in pname:
            plant_parts['leaves'] = pid
    
    # Get industry mapping
    industries = {}
    cursor.execute("SELECT id, LOWER(name) FROM industry_sub_categories")
    for iid, iname in cursor.fetchall():
        industries[iname.split()[0]] = iid  # Simple mapping
    
    # Default industry IDs for new categories
    default_industries = {
        'aerospace': 35,  # Electronics/Tech
        'environmental': 12,  # Agriculture
        'marine': 1,  # General
        'water': 12,  # Agriculture
        'sports': 23,  # Accessories
        'pets': 23,  # Accessories
        'art': 31,  # Specialty
        'music': 31,  # Specialty
        'emergency': 1,  # General
        'survival': 1,  # General
        'safety': 1,  # General
        'medical': 44,  # Therapeutic
        'baby': 22,  # Body Care
        'garden': 12,  # Agriculture
        'office': 31,  # Specialty
        'school': 31,  # Specialty
    }
    
    added = 0
    
    for name, desc, part, industry in MORE_UNIQUE_PRODUCTS:
        try:
            # Check if exists
            cursor.execute("SELECT id FROM uses_products WHERE LOWER(name) = LOWER(%s)", (name,))
            if cursor.fetchone():
                print(f"⚠️  Already exists: {name}")
                continue
            
            # Get IDs
            part_id = plant_parts.get(part, 2)
            industry_id = industries.get(industry, default_industries.get(industry, 1))
            
            # Generate benefits
            benefits = [
                "Innovative sustainable solution",
                "Superior performance metrics",
                "Environmentally responsible",
                "Cost-effective alternative",
                "Future-ready technology"
            ]
            
            # Insert
            cursor.execute("""
                INSERT INTO uses_products (
                    name, description, plant_part_id, industry_sub_category_id,
                    benefits_advantages, created_at, updated_at,
                    source_type, source_agent, data_completeness_score,
                    verification_status, data_version
                ) VALUES (%s, %s, %s, %s, %s, NOW(), NOW(), %s, %s, %s, %s, %s)
            """, (
                name, desc, part_id, industry_id, benefits,
                'ai_agent', 'unique_generator_v2', 90, 'unverified', 1
            ))
            
            conn.commit()
            added += 1
            print(f"✅ Added: {name}")
            
        except Exception as e:
            conn.rollback()
            print(f"❌ Error adding {name}: {e}")
    
    cursor.execute("SELECT COUNT(*) FROM uses_products")
    total = cursor.fetchone()[0]
    
    print(f"\n📊 Summary:")
    print(f"Added: {added} unique products")
    print(f"Total products: {total}")
    
    conn.close()

if __name__ == "__main__":
    add_more_unique_products()