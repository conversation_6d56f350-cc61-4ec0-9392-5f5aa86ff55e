#!/usr/bin/env python3
"""
Fix the stuck Hemp Milk Concentrate product and prevent infinite loops
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

conn = psycopg2.connect(os.getenv('DATABASE_URL'))
cur = conn.cursor()

# First, let's manually update the description to be longer
# This will remove it from the low quality list
update_query = """
UPDATE uses_products 
SET description = %s,
    updated_at = NOW(),
    source_agent = COALESCE(source_agent, 'manual') || '_quality_improvement'
WHERE name = 'Hemp Milk Concentrate'
"""

new_description = """
Innovative concentrated hemp milk base revolutionizing plant-based beverage production with 90% packaging waste reduction. 
This premium concentrate transforms dairy alternatives by delivering exceptional nutritional value in a compact, shelf-stable format. 
One 8-pound pail yields an impressive 60 quarts of creamy, nutrient-rich hemp milk with an extended 18-month shelf life. 
Perfect for commercial operations, cafes, and health-conscious consumers seeking sustainable, efficient plant-based milk solutions. 
The concentrate features optimized protein content, essential fatty acids, and minerals while eliminating traditional packaging waste.
"""

cur.execute(update_query, (new_description,))
conn.commit()

print("✅ Fixed Hemp Milk Concentrate description")

# Check if there are any other products with quality score exactly 0.7
# that might cause similar issues
cur.execute("""
    SELECT name, LENGTH(description) as desc_len,
           CASE 
               WHEN LENGTH(description) < 100 THEN 0.3
               WHEN LENGTH(description) < 200 THEN 0.5
               WHEN LENGTH(description) < 300 THEN 0.7
               ELSE 0.8
           END +
           CASE 
               WHEN benefits_advantages IS NOT NULL AND cardinality(benefits_advantages) > 0 THEN 0.1
               ELSE 0
           END +
           CASE 
               WHEN technical_specifications IS NOT NULL THEN 0.1
               ELSE 0
           END as quality_score
    FROM uses_products
    WHERE COALESCE(source_agent, '') NOT LIKE '%quality_improvement%'
    AND LENGTH(description) BETWEEN 140 AND 199
    AND benefits_advantages IS NOT NULL 
    AND cardinality(benefits_advantages) >= 3
    AND technical_specifications IS NOT NULL
    AND manufacturing_processes_summary IS NOT NULL
""")

similar_products = cur.fetchall()

if similar_products:
    print(f"\n⚠️ Found {len(similar_products)} similar products that might cause the same issue:")
    for prod in similar_products:
        print(f"  - {prod[0]} (desc length: {prod[1]}, quality: {prod[2]})")
else:
    print("\n✅ No other products with similar edge case found")

cur.close()
conn.close()

print("\n💡 To prevent this in the future, update the quality improvement agent to:")
print("   1. Track failed attempts per product")
print("   2. Skip products after 3 failed attempts")
print("   3. Handle edge cases where only description needs improvement")