#!/usr/bin/env python3
"""
Link Products to Companies - Intelligent Matching Algorithm
Matches 4,623 unlinked products to appropriate companies based on:
- Name similarity
- Industry alignment
- Description matching
- Website content
"""

import os
import re
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher
from supabase import create_client, Client
import asyncio
import aiohttp
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class MatchScore:
    product_id: int
    company_id: int
    name_score: float
    industry_score: float
    description_score: float
    total_score: float
    confidence: str  # high, medium, low

class ProductCompanyLinker:
    def __init__(self):
        self.supabase = self._init_supabase()
        self.products_cache = {}
        self.companies_cache = {}
        self.industry_mapping = {}
        
    def _init_supabase(self) -> Client:
        """Initialize Supabase client"""
        url = os.getenv('VITE_SUPABASE_URL', 'https://ktoqznqmlnxrtvubewyz.supabase.co')
        key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        if not key:
            key = os.getenv('VITE_SUPABASE_ANON_KEY')
        return create_client(url, key)
    
    def load_unlinked_products(self) -> List[Dict]:
        """Load products without company assignments"""
        logger.info("Loading unlinked products...")
        
        # Get products without companies
        query = """
        SELECT p.id, p.name, p.description, p.industry_sub_category_id,
               isc.name as industry_name, pp.name as plant_part_name
        FROM uses_products p
        LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
        LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
        WHERE NOT EXISTS (
            SELECT 1 FROM hemp_company_products hcp 
            WHERE hcp.product_id = p.id
        )
        LIMIT 500
        """
        
        result = self.supabase.rpc('execute_sql', {'query': query}).execute()
        products = result.data if result.data else []
        
        logger.info(f"Found {len(products)} unlinked products")
        
        # Cache products
        for product in products:
            self.products_cache[product['id']] = product
            
        return products
    
    def load_companies(self) -> List[Dict]:
        """Load all companies with their focus areas"""
        logger.info("Loading companies...")
        
        result = self.supabase.table('hemp_companies').select(
            'id, name, description, website, primary_focus, company_type'
        ).execute()
        
        companies = result.data
        logger.info(f"Loaded {len(companies)} companies")
        
        # Cache companies
        for company in companies:
            self.companies_cache[company['id']] = company
            
        return companies
    
    def calculate_name_similarity(self, product_name: str, company_name: str) -> float:
        """Calculate similarity between product and company names"""
        # Clean names
        product_clean = re.sub(r'[^\w\s]', '', product_name.lower())
        company_clean = re.sub(r'[^\w\s]', '', company_name.lower())
        
        # Check if company name appears in product name
        if company_clean in product_clean:
            return 0.9
        
        # Check word overlap
        product_words = set(product_clean.split())
        company_words = set(company_clean.split())
        
        if company_words & product_words:
            overlap = len(company_words & product_words) / len(company_words)
            return min(0.8, overlap)
        
        # Use sequence matcher for general similarity
        return SequenceMatcher(None, product_clean, company_clean).ratio() * 0.5
    
    def calculate_industry_alignment(self, product: Dict, company: Dict) -> float:
        """Calculate industry alignment score"""
        score = 0.0
        
        # Check if company focus matches product industry
        if company.get('primary_focus') and product.get('industry_name'):
            company_focus = company['primary_focus'].lower()
            product_industry = product['industry_name'].lower()
            
            # Direct match
            if company_focus in product_industry or product_industry in company_focus:
                score = 0.9
            # Related industries
            elif any(word in company_focus for word in product_industry.split()):
                score = 0.6
        
        # Check company type alignment
        if company.get('company_type'):
            company_type = company['company_type'].lower()
            
            # Manufacturer matches most products
            if 'manufacturer' in company_type or 'producer' in company_type:
                score = max(score, 0.5)
            # Processor for specific plant parts
            elif 'processor' in company_type and product.get('plant_part_name'):
                if product['plant_part_name'].lower() in ['bast fiber', 'hurd', 'seeds']:
                    score = max(score, 0.7)
            # Retailer/distributor for finished products
            elif 'retail' in company_type or 'distributor' in company_type:
                if product.get('description') and 'finished' in product['description'].lower():
                    score = max(score, 0.6)
        
        return score
    
    def calculate_description_similarity(self, product: Dict, company: Dict) -> float:
        """Calculate description-based similarity"""
        product_desc = (product.get('description') or '').lower()
        company_desc = (company.get('description') or '').lower()
        
        if not product_desc or not company_desc:
            return 0.0
        
        # Extract key terms
        product_terms = set(re.findall(r'\b\w{4,}\b', product_desc))
        company_terms = set(re.findall(r'\b\w{4,}\b', company_desc))
        
        # Remove common words
        common_words = {'hemp', 'product', 'made', 'from', 'with', 'used', 'this', 'that', 'have', 'been'}
        product_terms -= common_words
        company_terms -= common_words
        
        if not company_terms:
            return 0.0
        
        # Calculate overlap
        overlap = len(product_terms & company_terms) / len(company_terms)
        return min(1.0, overlap * 2)  # Scale up since descriptions are often brief
    
    def find_best_matches(self, product: Dict, companies: List[Dict], threshold: float = 0.4) -> List[MatchScore]:
        """Find best company matches for a product"""
        matches = []
        
        for company in companies:
            # Calculate individual scores
            name_score = self.calculate_name_similarity(product['name'], company['name'])
            industry_score = self.calculate_industry_alignment(product, company)
            desc_score = self.calculate_description_similarity(product, company)
            
            # Weighted total score
            total_score = (
                name_score * 0.4 +
                industry_score * 0.4 +
                desc_score * 0.2
            )
            
            if total_score >= threshold:
                # Determine confidence level
                if total_score >= 0.7:
                    confidence = 'high'
                elif total_score >= 0.5:
                    confidence = 'medium'
                else:
                    confidence = 'low'
                
                match = MatchScore(
                    product_id=product['id'],
                    company_id=company['id'],
                    name_score=name_score,
                    industry_score=industry_score,
                    description_score=desc_score,
                    total_score=total_score,
                    confidence=confidence
                )
                matches.append(match)
        
        # Sort by total score
        matches.sort(key=lambda x: x.total_score, reverse=True)
        
        # Return top 3 matches
        return matches[:3]
    
    def create_product_company_links(self, matches: List[MatchScore], auto_approve_threshold: float = 0.7):
        """Create links between products and companies"""
        created_count = 0
        
        for match in matches:
            # Only auto-link high confidence matches
            if match.total_score >= auto_approve_threshold:
                try:
                    # Check if link already exists
                    existing = self.supabase.table('hemp_company_products').select('id').eq(
                        'product_id', match.product_id
                    ).eq('company_id', match.company_id).execute()
                    
                    if not existing.data:
                        # Create link
                        link_data = {
                            'product_id': match.product_id,
                            'company_id': match.company_id,
                            'relationship_type': 'manufacturer' if match.confidence == 'high' else 'supplier',
                            'is_primary': match.total_score >= 0.8,
                            'verified': False,
                            'notes': f"Auto-matched with {match.confidence} confidence (score: {match.total_score:.2f})"
                        }
                        
                        self.supabase.table('hemp_company_products').insert(link_data).execute()
                        created_count += 1
                        
                        product = self.products_cache.get(match.product_id, {})
                        company = self.companies_cache.get(match.company_id, {})
                        
                        logger.info(
                            f"Linked: {product.get('name', 'Unknown')} -> "
                            f"{company.get('name', 'Unknown')} "
                            f"(confidence: {match.confidence}, score: {match.total_score:.2f})"
                        )
                except Exception as e:
                    logger.error(f"Error creating link: {e}")
        
        return created_count
    
    def run_matching_process(self):
        """Main process to match products with companies"""
        logger.info("Starting product-company matching process...")
        
        # Load data
        products = self.load_unlinked_products()
        companies = self.load_companies()
        
        if not products:
            logger.info("No unlinked products found")
            return
        
        if not companies:
            logger.error("No companies found in database")
            return
        
        # Process matching
        total_matches = 0
        high_confidence = 0
        medium_confidence = 0
        low_confidence = 0
        
        for i, product in enumerate(products):
            if i % 50 == 0:
                logger.info(f"Processing product {i}/{len(products)}...")
            
            # Find matches
            matches = self.find_best_matches(product, companies)
            
            if matches:
                # Create links for high-confidence matches
                created = self.create_product_company_links(matches)
                total_matches += created
                
                # Count confidence levels
                for match in matches:
                    if match.confidence == 'high':
                        high_confidence += 1
                    elif match.confidence == 'medium':
                        medium_confidence += 1
                    else:
                        low_confidence += 1
        
        # Final report
        logger.info("\n" + "="*50)
        logger.info("MATCHING PROCESS COMPLETE")
        logger.info(f"Products processed: {len(products)}")
        logger.info(f"Total matches created: {total_matches}")
        logger.info(f"High confidence: {high_confidence}")
        logger.info(f"Medium confidence: {medium_confidence}")
        logger.info(f"Low confidence: {low_confidence}")
        logger.info("="*50)
        
        return {
            'products_processed': len(products),
            'matches_created': total_matches,
            'high_confidence': high_confidence,
            'medium_confidence': medium_confidence,
            'low_confidence': low_confidence
        }

def main():
    """Main entry point"""
    linker = ProductCompanyLinker()
    results = linker.run_matching_process()
    
    # Check if we should continue with more batches
    if results['matches_created'] > 0:
        logger.info("\nWould you like to process another batch? The script processes 500 products at a time.")
        logger.info("Run again to continue matching more products.")

if __name__ == "__main__":
    main()