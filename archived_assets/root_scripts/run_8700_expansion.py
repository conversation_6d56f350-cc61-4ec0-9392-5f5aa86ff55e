#!/usr/bin/env python3
"""
Master execution script for Hemp Database 8,700 Product Expansion
Orchestrates all agents to achieve proper distribution and spec-level depth
"""

import sys
import os
import json
import time
from datetime import datetime
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/expansion_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check that required environment variables are set"""
    required_vars = ['DATABASE_URL']
    missing = []
    
    for var in required_vars:
        if not os.environ.get(var):
            missing.append(var)
    
    if missing:
        logger.error(f"Missing required environment variables: {missing}")
        logger.info("Please set: export DATABASE_URL='your_database_url'")
        return False
    
    return True

def run_expansion_cycle():
    """Run one complete expansion cycle"""
    
    try:
        # Import agents
        from agents.orchestrator.product_expansion_orchestrator import ProductExpansionOrchestrator
        from agents.specialized.plant_part_rebalancer import PlantPartRebalancerAgent
        from agents.specialized.spec_level_expander import SpecLevelExpansionAgent
        
        logger.info("=" * 60)
        logger.info("Starting Hemp Database 8,700 Product Expansion Cycle")
        logger.info("=" * 60)
        
        # Step 1: Analyze current state
        orchestrator = ProductExpansionOrchestrator()
        analysis = orchestrator.analyze_current_state()
        
        logger.info(f"\n📊 Current Status:")
        logger.info(f"   Total Products: {analysis['total_products']}")
        logger.info(f"   Target: 8,700")
        logger.info(f"   Completion: {analysis['percentage_complete']}%")
        logger.info(f"   Gap: {8700 - analysis['total_products']} products")
        
        # Show top gaps
        logger.info(f"\n🎯 Top Priority Gaps:")
        for gap in analysis['distribution_gaps'][:5]:
            if gap['gap'] > 0:
                logger.info(f"   - {gap['plant_part']}: Need {gap['gap']} more products")
        
        # Step 2: Run Plant Part Rebalancer
        logger.info(f"\n🔄 Running Plant Part Rebalancer...")
        rebalancer = PlantPartRebalancerAgent()
        rebalance_results = rebalancer.rebalance_all()
        
        total_rebalanced = sum(
            r.get('generated', 0) for r in rebalance_results.get('generated', {}).values()
        )
        logger.info(f"   Generated {total_rebalanced} family-level products")
        
        # Step 3: Expand families to specs
        logger.info(f"\n🔬 Running Spec-Level Expansion...")
        expander = SpecLevelExpansionAgent()
        expansion_results = expander.expand_recent_families(hours=1)
        
        logger.info(f"   Expanded {expansion_results['candidates_found']} families")
        logger.info(f"   Generated {expansion_results['variants_generated']} spec variants")
        logger.info(f"   Inserted {expansion_results['variants_inserted']} new products")
        
        # Step 4: Show category breakdown
        if expansion_results.get('by_category'):
            logger.info(f"\n📦 Expansion by Category:")
            for category, stats in expansion_results['by_category'].items():
                logger.info(f"   - {category}: {stats['variants_inserted']} variants from {stats['families']} families")
        
        # Step 5: Calculate new totals
        new_analysis = orchestrator.analyze_current_state()
        products_added = new_analysis['total_products'] - analysis['total_products']
        
        logger.info(f"\n✅ Cycle Complete:")
        logger.info(f"   Products Added: {products_added}")
        logger.info(f"   New Total: {new_analysis['total_products']}")
        logger.info(f"   New Completion: {new_analysis['percentage_complete']}%")
        
        # Calculate next milestone
        next_milestone = orchestrator.calculate_next_milestone(new_analysis['total_products'])
        if next_milestone.get('target'):
            logger.info(f"\n🎯 Next Milestone: {next_milestone['target']} ({next_milestone['products_needed']} products needed)")
        
        return {
            'success': True,
            'products_added': products_added,
            'new_total': new_analysis['total_products'],
            'completion': new_analysis['percentage_complete']
        }
        
    except ImportError as e:
        logger.error(f"Failed to import agents: {e}")
        logger.info("\nCreating placeholder agents...")
        create_placeholder_agents()
        return {'success': False, 'error': str(e)}
        
    except Exception as e:
        logger.error(f"Expansion cycle failed: {e}")
        return {'success': False, 'error': str(e)}

def create_placeholder_agents():
    """Create placeholder agent files if they don't exist"""
    agents_to_create = [
        ('src/agents/specialized/bast_fiber_specialist.py', 'BastFiberSpecialistAgent'),
        ('src/agents/specialized/hurd_construction_agent.py', 'HurdConstructionAgent'),
        ('src/agents/specialized/energy_products_agent.py', 'EnergyProductsAgent')
    ]
    
    for filepath, class_name in agents_to_create:
        if not os.path.exists(filepath):
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            placeholder_code = f'''#!/usr/bin/env python3
"""
{class_name} - Placeholder implementation
"""

class {class_name}:
    def __init__(self):
        self.name = "{class_name}"
    
    def generate_products(self, plant_part: str, count: int = 10):
        """Placeholder method"""
        return {{"generated": 0, "reason": "not_implemented"}}
'''
            with open(filepath, 'w') as f:
                f.write(placeholder_code)
            
            logger.info(f"Created placeholder: {filepath}")

def run_continuous_expansion(max_cycles: int = 10, target_products: int = 8700):
    """Run continuous expansion until target is reached or max cycles"""
    
    if not check_environment():
        return
    
    logger.info(f"\n🚀 Starting Continuous Expansion")
    logger.info(f"   Target: {target_products} products")
    logger.info(f"   Max Cycles: {max_cycles}")
    
    for cycle in range(1, max_cycles + 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"CYCLE {cycle}/{max_cycles}")
        logger.info(f"{'='*60}")
        
        result = run_expansion_cycle()
        
        if not result['success']:
            logger.error(f"Cycle {cycle} failed: {result.get('error')}")
            break
        
        # Check if target reached
        if result['new_total'] >= target_products:
            logger.info(f"\n🎉 TARGET REACHED! {result['new_total']} products")
            break
        
        # Check if no progress
        if result['products_added'] == 0:
            logger.warning(f"No products added in cycle {cycle}. Stopping.")
            break
        
        # Wait between cycles
        if cycle < max_cycles:
            logger.info(f"\n⏳ Waiting 30 seconds before next cycle...")
            time.sleep(30)
    
    logger.info(f"\n{'='*60}")
    logger.info("Expansion Complete")
    logger.info(f"{'='*60}")

def show_dashboard():
    """Display current database statistics"""
    try:
        import psycopg2
        from psycopg2.extras import RealDictCursor
        
        conn = psycopg2.connect(os.environ.get('DATABASE_URL'))
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get expansion metrics
        cur.execute("SELECT * FROM get_expansion_metrics()")
        metrics = {row['metric_name']: row['metric_value'] for row in cur.fetchall()}
        
        # Get plant part distribution
        cur.execute("""
            SELECT * FROM expansion_progress 
            ORDER BY percent_complete DESC
        """)
        distribution = cur.fetchall()
        
        print("\n" + "="*60)
        print("HEMP DATABASE EXPANSION DASHBOARD")
        print("="*60)
        
        print(f"\n📊 Overall Metrics:")
        print(f"   Total Products: {int(metrics.get('total_products', 0)):,}")
        print(f"   Family Products: {int(metrics.get('family_products', 0)):,}")
        print(f"   Spec Products: {int(metrics.get('spec_products', 0)):,}")
        print(f"   Avg Variants/Family: {metrics.get('avg_variants_per_family', 0):.2f}")
        print(f"   Completion: {metrics.get('completion_percentage', 0):.1f}%")
        
        print(f"\n🌿 Plant Part Distribution:")
        print(f"{'Plant Part':<25} {'Current':<10} {'Target':<10} {'Complete':<10}")
        print("-" * 55)
        for row in distribution:
            current = int(row['total'])
            target = int(row['target']) if row['target'] else 0
            complete = f"{row['percent_complete']:.1f}%" if row['percent_complete'] else "N/A"
            print(f"{row['plant_part']:<25} {current:<10} {target:<10} {complete:<10}")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Failed to show dashboard: {e}")

def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Hemp Database 8,700 Product Expansion')
    parser.add_argument('--cycles', type=int, default=1, help='Number of expansion cycles to run')
    parser.add_argument('--continuous', action='store_true', help='Run continuous expansion')
    parser.add_argument('--dashboard', action='store_true', help='Show current dashboard')
    parser.add_argument('--target', type=int, default=8700, help='Target number of products')
    
    args = parser.parse_args()
    
    if args.dashboard:
        show_dashboard()
    elif args.continuous:
        run_continuous_expansion(max_cycles=args.cycles, target_products=args.target)
    else:
        for _ in range(args.cycles):
            run_expansion_cycle()
            if args.cycles > 1:
                time.sleep(30)

if __name__ == "__main__":
    main()