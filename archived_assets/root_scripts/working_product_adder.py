#!/usr/bin/env python3
"""
Working product adder with correct column types
"""
import os
import sys
import json
import psycopg2
import re
from datetime import datetime
from dotenv import load_dotenv

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from ai_providers.multi_provider import MultiProviderAI

load_dotenv()

def add_products_now():
    """Add products with correct data types"""
    ai = MultiProviderAI()
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    # Get starting count
    cursor.execute("SELECT COUNT(*) FROM uses_products")
    start_count = cursor.fetchone()[0]
    print(f"📊 Starting with {start_count} products\n")
    
    # Focus on underrepresented plant parts
    cursor.execute("""
        SELECT pp.id, pp.name, COUNT(up.id) as product_count
        FROM plant_parts pp
        LEFT JOIN uses_products up ON pp.id = up.plant_part_id
        GROUP BY pp.id, pp.name
        ORDER BY product_count ASC
    """)
    
    plant_parts_data = cursor.fetchall()
    total_added = 0
    
    for part_id, part_name, current_count in plant_parts_data[:4]:  # Focus on top 4 underrepresented
        print(f"🌿 {part_name} (current: {current_count} products)")
        
        # Generate 5 products per part
        prompt = f"""Generate exactly 5 innovative {part_name} hemp products.

Return ONLY product names and descriptions in this format:
1. Product Name - Clear description of the product and its use
2. Product Name - Clear description of the product and its use
3. Product Name - Clear description of the product and its use
4. Product Name - Clear description of the product and its use
5. Product Name - Clear description of the product and its use

Make each description at least 50 characters. Be specific and technical."""
        
        try:
            response = ai.generate(prompt, temperature=0.8)
            
            if response.success:
                # Parse the response
                lines = response.content.strip().split('\n')
                
                for line in lines:
                    # Match pattern: number. Name - Description
                    match = re.match(r'^\d+\.\s*([^-]+)\s*-\s*(.+)$', line.strip())
                    if not match:
                        continue
                    
                    name = match.group(1).strip()
                    description = match.group(2).strip()
                    
                    if len(name) < 5 or len(description) < 30:
                        continue
                    
                    # Check duplicate
                    cursor.execute("SELECT id FROM uses_products WHERE name = %s", (name,))
                    if cursor.fetchone():
                        print(f"  ⚠️  Duplicate: {name}")
                        continue
                    
                    # Prepare data with correct types
                    benefits = [
                        f"Made from sustainable {part_name.lower()}",
                        "Environmentally friendly alternative",
                        "High-performance hemp-based solution"
                    ]
                    
                    tech_specs = {
                        "material": f"{part_name} hemp",
                        "processing": "Advanced extraction/processing",
                        "sustainability": "Carbon negative production"
                    }
                    
                    # Insert with correct types
                    try:
                        cursor.execute("""
                            INSERT INTO uses_products (
                                name, 
                                description, 
                                plant_part_id,
                                benefits_advantages,
                                technical_specifications,
                                created_at,
                                updated_at
                            ) VALUES (%s, %s, %s, %s, %s::jsonb, NOW(), NOW())
                            RETURNING id
                        """, (
                            name[:100],  # Limit name length
                            description[:500],  # Limit description length
                            part_id,
                            benefits,  # Array type
                            json.dumps(tech_specs)  # JSONB type
                        ))
                        
                        new_id = cursor.fetchone()[0]
                        conn.commit()
                        
                        total_added += 1
                        print(f"  ✅ Added: {name} (ID: {new_id})")
                        
                    except Exception as e:
                        print(f"  ❌ Insert error: {str(e)[:100]}")
                        conn.rollback()
                
            else:
                print(f"  ❌ AI Error: {response.error}")
                
        except Exception as e:
            print(f"  ❌ Error: {str(e)[:100]}")
        
        print()  # Blank line
    
    # Final stats
    cursor.execute("SELECT COUNT(*) FROM uses_products")
    end_count = cursor.fetchone()[0]
    
    print("=" * 60)
    print(f"📊 FINAL RESULTS:")
    print(f"  Started: {start_count} products")
    print(f"  Added: {total_added} new products")
    print(f"  Total: {end_count} products")
    
    if total_added > 0:
        print(f"  Growth: +{((end_count - start_count) / start_count * 100):.1f}%")
        print(f"\n✅ Successfully added {total_added} products!")
    else:
        print("\n⚠️  No products added. Check the logs above for issues.")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    add_products_now()