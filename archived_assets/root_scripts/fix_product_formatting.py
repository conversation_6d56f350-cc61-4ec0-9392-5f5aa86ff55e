#!/usr/bin/env python3
"""
Fix product formatting issues:
1. Remove ** from product names
2. Regenerate images for remaining Unsplash placeholders
"""
import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def fix_product_names():
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    print("🔧 FIXING PRODUCT FORMATTING ISSUES")
    print("="*60)
    
    # First, check products with ** in names
    cursor.execute("""
        SELECT id, name 
        FROM uses_products 
        WHERE name LIKE '%**%'
        ORDER BY name
    """)
    
    products_with_asterisks = cursor.fetchall()
    print(f"\n📊 Found {len(products_with_asterisks)} products with ** in names")
    
    if products_with_asterisks:
        print("\nSample products to fix:")
        for id, name in products_with_asterisks[:5]:
            clean_name = name.replace('**', '')
            print(f"  {name} → {clean_name}")
        
        # Fix all product names
        print(f"\n🔧 Fixing {len(products_with_asterisks)} product names...")
        
        fixed_count = 0
        for id, name in products_with_asterisks:
            clean_name = name.replace('**', '').strip()
            
            try:
                cursor.execute("""
                    UPDATE uses_products
                    SET name = %s,
                        updated_at = NOW()
                    WHERE id = %s
                """, (clean_name, id))
                
                fixed_count += 1
                
            except Exception as e:
                print(f"Error fixing product {id}: {e}")
                conn.rollback()
                continue
        
        conn.commit()
        print(f"✅ Fixed {fixed_count} product names")
    
    # Check for remaining Unsplash images
    print("\n\n🖼️ CHECKING REMAINING PLACEHOLDER IMAGES")
    print("="*60)
    
    cursor.execute("""
        SELECT id, name, image_url, created_at
        FROM uses_products
        WHERE image_url LIKE '%unsplash%'
        ORDER BY created_at DESC
    """)
    
    unsplash_products = cursor.fetchall()
    print(f"\n📊 Found {len(unsplash_products)} products still using Unsplash images")
    
    if unsplash_products:
        print("\nProducts with placeholder images:")
        for id, name, url, created in unsplash_products:
            print(f"\n{name}")
            print(f"  ID: {id}")
            print(f"  Created: {created}")
            print(f"  URL: {url[:60]}...")
        
        # Add these to the AI generation queue
        print("\n🚩 Adding to AI generation queue...")
        
        added_count = 0
        for id, name, _, _ in unsplash_products:
            try:
                # Get plant part ID
                cursor.execute("""
                    SELECT plant_part_id FROM uses_products WHERE id = %s
                """, (id,))
                plant_part_id = cursor.fetchone()[0]
                
                # Add to queue (or update if exists)
                cursor.execute("""
                    INSERT INTO ai_image_generation_queue 
                    (product_id, product_name, plant_part_id, priority, status)
                    VALUES (%s, %s, %s, %s, %s)
                    ON CONFLICT (product_id) 
                    DO UPDATE SET 
                        status = 'pending',
                        priority = 10,
                        created_at = NOW()
                """, (id, name, plant_part_id, 10, 'pending'))
                
                added_count += 1
                
            except Exception as e:
                print(f"Error adding product {id} to queue: {e}")
                conn.rollback()
                continue
        
        conn.commit()
        print(f"✅ Added {added_count} products to AI generation queue")
    
    # Check other potential formatting issues
    print("\n\n🔍 CHECKING OTHER FORMATTING ISSUES")
    print("="*60)
    
    # Check for HTML entities
    cursor.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE name LIKE '%&%' OR name LIKE '%<%' OR name LIKE '%>%'
    """)
    html_count = cursor.fetchone()[0]
    print(f"Products with potential HTML entities: {html_count}")
    
    # Check for excessive whitespace
    cursor.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE name LIKE '%  %' OR name != TRIM(name)
    """)
    whitespace_count = cursor.fetchone()[0]
    print(f"Products with whitespace issues: {whitespace_count}")
    
    # Final summary
    print("\n\n📊 SUMMARY")
    print("="*60)
    
    cursor.execute("""
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN image_url LIKE '%replicate%' THEN 1 END) as ai_images,
            COUNT(CASE WHEN image_url LIKE '%unsplash%' THEN 1 END) as placeholders,
            COUNT(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 END) as no_images
        FROM uses_products
    """)
    
    total, ai_images, placeholders, no_images = cursor.fetchone()
    
    print(f"Total products: {total}")
    print(f"With AI images: {ai_images} ({ai_images/total*100:.1f}%)")
    print(f"With placeholders: {placeholders} ({placeholders/total*100:.1f}%)")
    print(f"Without images: {no_images} ({no_images/total*100:.1f}%)")
    
    if placeholders > 0:
        print(f"\n💡 Next step: Run 'python generate_product_images.py --limit {placeholders}' to generate AI images for remaining products")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    fix_product_names()