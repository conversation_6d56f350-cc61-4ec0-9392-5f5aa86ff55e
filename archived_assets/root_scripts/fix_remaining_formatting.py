#!/usr/bin/env python3
"""Fix remaining formatting issues"""
import os
import psycopg2
import re
from dotenv import load_dotenv

load_dotenv()
conn = psycopg2.connect(os.getenv('DATABASE_URL'))
cursor = conn.cursor()

print('🔧 FIXING REMAINING FORMATTING ISSUES')
print('='*60)

# More comprehensive ** removal pattern
def clean_text(text):
    if not text:
        return text
    # Remove all ** patterns
    text = re.sub(r'\*\*+', '', text)
    # Clean up extra spaces
    text = re.sub(r'\s+', ' ', text)
    # Remove leading/trailing spaces
    text = text.strip()
    return text

# Fix names
cursor.execute("SELECT id, name FROM uses_products WHERE name LIKE '%**%'")
names_to_fix = cursor.fetchall()
for id, name in names_to_fix:
    clean_name = clean_text(name)
    cursor.execute("UPDATE uses_products SET name = %s WHERE id = %s", (clean_name, id))
    print(f"Fixed name for ID {id}: {name[:50]}... → {clean_name[:50]}...")

# Fix descriptions
cursor.execute("SELECT id, description FROM uses_products WHERE description LIKE '%**%'")
descs_to_fix = cursor.fetchall()
for id, desc in descs_to_fix:
    clean_desc = clean_text(desc)
    cursor.execute("UPDATE uses_products SET description = %s WHERE id = %s", (clean_desc, id))
    print(f"Fixed description for ID {id}")

# Fix benefits
cursor.execute("""
    SELECT id, benefits_advantages 
    FROM uses_products 
    WHERE array_to_string(benefits_advantages, ' ') LIKE '%**%'
""")
benefits_to_fix = cursor.fetchall()
for id, benefits in benefits_to_fix:
    if benefits:
        clean_benefits = [clean_text(b) for b in benefits if b]
        cursor.execute(
            "UPDATE uses_products SET benefits_advantages = %s WHERE id = %s", 
            (clean_benefits, id)
        )
        print(f"Fixed benefits for ID {id}")

conn.commit()

# Verify
cursor.execute("""
    SELECT COUNT(*) 
    FROM uses_products 
    WHERE name LIKE '%**%' 
       OR description LIKE '%**%'
       OR array_to_string(benefits_advantages, ' ') LIKE '%**%'
""")
remaining = cursor.fetchone()[0]
print(f"\n✅ Complete! Remaining products with ** formatting: {remaining}")

conn.close()