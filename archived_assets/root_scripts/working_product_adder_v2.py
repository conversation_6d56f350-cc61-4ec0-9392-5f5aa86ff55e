#!/usr/bin/env python3
"""
Working Product Adder V2 - With Quality Control
Uses the QualityControlledAgent base class
"""
import os
import sys
import random
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.agents.quality_controlled_agent import QualityControlledAgent
from dotenv import load_dotenv

load_dotenv()

class EnhancedProductAdder(QualityControlledAgent):
    """Enhanced product adder with quality control"""
    
    def __init__(self):
        super().__init__("working_product_adder_v2")
        self.product_templates = self._load_templates()
    
    def _load_templates(self):
        """Load high-quality product templates"""
        return [
            # Fiber Products
            {
                'plant_part': 'fiber',
                'templates': [
                    {
                        'name': 'Hemp Fiber Acoustic Panel System',
                        'description': 'Professional-grade acoustic treatment panels utilizing compressed hemp fiber technology. These panels provide superior sound absorption across all frequency ranges while maintaining structural integrity and fire resistance. The natural hemp fibers create a unique porous structure that effectively dampens sound waves, making them ideal for recording studios, theaters, and noise-sensitive environments.',
                        'benefits': [
                            'Superior noise reduction coefficient (NRC) of 0.95',
                            'Class A fire resistance without chemical treatments',
                            'Moisture-regulating properties prevent mold growth',
                            'Carbon-negative manufacturing process',
                            'Lightweight yet durable construction',
                            '50-year expected lifespan with minimal maintenance'
                        ]
                    },
                    {
                        'name': 'Hemp Biocomposite Automotive Parts',
                        'description': 'Advanced automotive components manufactured from hemp fiber-reinforced biocomposites. These parts replace traditional plastics and fiberglass in vehicle interiors, offering superior strength-to-weight ratios and environmental benefits. The hemp fibers provide excellent impact resistance and vibration dampening, while reducing overall vehicle weight for improved fuel efficiency.',
                        'benefits': [
                            '30% lighter than traditional plastic components',
                            'Improved crash safety due to controlled deformation',
                            'Natural UV resistance extends product life',
                            'Reduces vehicle carbon footprint by 20%',
                            'Fully recyclable at end of life',
                            'Cost-competitive with petroleum-based alternatives'
                        ]
                    }
                ]
            },
            # Seed Products
            {
                'plant_part': 'seed',
                'templates': [
                    {
                        'name': 'Hemp Seed Protein Concentrate 80%',
                        'description': 'Ultra-refined hemp seed protein concentrate achieving 80% protein content through advanced extraction methods. This premium plant-based protein features a complete amino acid profile with exceptional bioavailability. The gentle processing preserves natural enzymes and beneficial compounds while removing anti-nutritional factors, making it ideal for sports nutrition and therapeutic applications.',
                        'benefits': [
                            'Complete protein with all essential amino acids',
                            'Branched-chain amino acid (BCAA) content rivals whey',
                            'Hypoallergenic - suitable for sensitive individuals',
                            'Enhanced absorption through enzymatic pre-digestion',
                            'Rich in arginine for cardiovascular health',
                            'Neutral taste profile for versatile applications'
                        ]
                    },
                    {
                        'name': 'Cold-Pressed Hemp Seed Oil Capsules',
                        'description': 'Pharmaceutical-grade hemp seed oil encapsulated using advanced cold-pressing technology that preserves delicate omega fatty acids. Each capsule delivers a precise ratio of omega-3 to omega-6 fatty acids (1:3) optimal for human health. The enteric coating ensures maximum absorption while preventing fishy aftertaste common in omega supplements.',
                        'benefits': [
                            'Optimal 1:3 omega-3 to omega-6 ratio',
                            'Gamma-linolenic acid (GLA) for hormonal balance',
                            'Enteric coating for enhanced bioavailability',
                            'Third-party tested for purity and potency',
                            'Supports cognitive function and heart health',
                            'Sustainable alternative to fish oil supplements'
                        ]
                    }
                ]
            },
            # Hurd Products
            {
                'plant_part': 'hurd',
                'templates': [
                    {
                        'name': 'Hempcrete Building Blocks - Load Bearing',
                        'description': 'Revolutionary load-bearing hempcrete blocks engineered for structural applications in sustainable construction. These blocks combine hemp hurd with a proprietary lime-based binder to create a carbon-negative building material that actually strengthens over time. The unique cellular structure provides excellent thermal mass while allowing vapor permeability for healthy indoor environments.',
                        'benefits': [
                            'Carbon-negative - sequesters 110kg CO2 per cubic meter',
                            'R-value of 2.5 per inch for energy efficiency',
                            'Natural pest and fire resistance',
                            'Self-healing properties through ongoing carbonation',
                            'Regulates indoor humidity at 40-60% RH',
                            'Compressive strength increases 300% over 10 years'
                        ]
                    },
                    {
                        'name': 'Hemp Hurd Biochar Soil Amendment',
                        'description': 'Premium biochar produced from hemp hurd through controlled pyrolysis at optimal temperatures. This highly porous carbon material dramatically improves soil structure, water retention, and nutrient availability. The extensive surface area (500 m²/g) provides habitat for beneficial microorganisms while sequestering carbon for centuries.',
                        'benefits': [
                            'Increases soil water retention by 40%',
                            'Enhances cation exchange capacity for nutrient retention',
                            'Provides long-term carbon sequestration (500+ years)',
                            'Reduces fertilizer requirements by 30%',
                            'Improves soil pH buffering capacity',
                            'Increases crop yields by 15-25% in depleted soils'
                        ]
                    }
                ]
            },
            # Flower Products
            {
                'plant_part': 'flower',
                'templates': [
                    {
                        'name': 'Hemp Flower Terpene Complex - Stress Relief',
                        'description': 'Scientifically formulated terpene complex extracted from hemp flowers using supercritical CO2 technology. This synergistic blend combines beta-caryophyllene, linalool, and myrcene in precise ratios shown to activate the endocannabinoid system and promote relaxation. The alcohol-free formula ensures rapid sublingual absorption for fast-acting stress relief.',
                        'benefits': [
                            'Clinically proven terpene ratios for stress reduction',
                            'Activates CB2 receptors without psychoactive effects',
                            'Fast-acting sublingual absorption in 15 minutes',
                            'Non-drowsy formula suitable for daytime use',
                            'Third-party tested for purity and consistency',
                            'Synergistic entourage effect enhances efficacy'
                        ]
                    }
                ]
            }
        ]
    
    def generate_products(self, count=3):
        """Generate high-quality products with validation"""
        products_added = 0
        
        # Get plant part IDs
        plant_part_map = self._get_plant_part_map()
        industry_map = self._get_industry_map()
        
        for _ in range(count * 2):  # Try twice as many in case of failures
            if products_added >= count:
                break
                
            # Select random template
            category = random.choice(self.product_templates)
            template = random.choice(category['templates'])
            
            # Create product with variations
            product_name = self._create_variation(template['name'])
            
            product_data = {
                'name': product_name,
                'description': template['description'],
                'benefits_advantages': template['benefits'],
                'plant_part_id': plant_part_map.get(category['plant_part'], 1),
                'industry_sub_category_id': random.choice(list(industry_map.values()))
            }
            
            # Add product using quality control
            if self.add_product(product_data):
                products_added += 1
        
        return products_added
    
    def _create_variation(self, base_name):
        """Create name variation to avoid duplicates"""
        variations = [
            f"Advanced {base_name}",
            f"Premium {base_name}",
            f"Professional {base_name}",
            f"Industrial {base_name}",
            f"Eco-Certified {base_name}",
            f"Next-Gen {base_name}",
            f"High-Performance {base_name}",
            f"Sustainable {base_name}"
        ]
        return random.choice(variations)
    
    def _get_plant_part_map(self):
        """Get mapping of plant part names to IDs"""
        self.cursor.execute("SELECT id, LOWER(name) FROM plant_parts")
        
        part_map = {}
        for pid, name in self.cursor.fetchall():
            if 'fiber' in name or 'bast' in name:
                part_map['fiber'] = pid
            elif 'seed' in name:
                part_map['seed'] = pid
            elif 'hurd' in name or 'shiv' in name:
                part_map['hurd'] = pid
            elif 'flower' in name:
                part_map['flower'] = pid
        
        return part_map
    
    def _get_industry_map(self):
        """Get mapping of industries"""
        self.cursor.execute("SELECT id, name FROM industry_sub_categories")
        return {name: id for id, name in self.cursor.fetchall()}


def main():
    print("🚀 Enhanced Product Adder V2 - With Quality Control")
    print("="*60)
    
    adder = EnhancedProductAdder()
    
    try:
        # Get current count
        adder.cursor.execute("SELECT COUNT(*) FROM uses_products")
        before_count = adder.cursor.fetchone()[0]
        
        # Add products
        added = adder.generate_products(count=3)
        
        # Get new count
        adder.cursor.execute("SELECT COUNT(*) FROM uses_products")
        after_count = adder.cursor.fetchone()[0]
        
        print(f"\n📊 Results:")
        print(f"Products before: {before_count}")
        print(f"Products after: {after_count}")
        print(f"Added: {added} new products")
        
    finally:
        adder.close()


if __name__ == "__main__":
    main()