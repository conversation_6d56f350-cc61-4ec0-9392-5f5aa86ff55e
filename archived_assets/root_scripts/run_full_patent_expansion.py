#!/usr/bin/env python3
"""
Run full patent expansion - all 124 patents
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.specialized.patent_mining_agent_api import APIPatentMiningAgent

print("🚀 Starting FULL Patent Mining Expansion...")
print("=" * 60)

# Run the agent
agent = APIPatentMiningAgent()

# Process ALL 124 patents
results = agent.run_discovery()  # No limit = all patents

print("\n" + "=" * 60)
print(f"Patent mining complete!")
print(f"New products added: {results['products_saved']}")
print(f"Duplicates skipped: {results['duplicates']}") 
print(f"Errors: {results['errors']}")
print(f"\n🎯 Database expansion progress: {1106 + 73 + 56 + results['products_saved']} products")