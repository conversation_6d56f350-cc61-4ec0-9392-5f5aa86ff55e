#!/usr/bin/env python3
"""
Advanced Search Infrastructure Setup for Hemp Database
Creates search views, indexes, and faceted search capabilities
"""

import os
import logging
from supabase import create_client, Client
from typing import Dict, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SearchInfrastructureSetup:
    def __init__(self):
        self.supabase = self._init_supabase()
        
    def _init_supabase(self) -> Client:
        """Initialize Supabase client"""
        url = os.getenv('VITE_SUPABASE_URL', 'https://ktoqznqmlnxrtvubewyz.supabase.co')
        key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        if not key:
            key = os.getenv('VITE_SUPABASE_ANON_KEY')
        return create_client(url, key)
    
    def create_search_views(self):
        """Create comprehensive search views"""
        logger.info("Creating search infrastructure views...")
        
        # Main product search view with all relevant data
        search_view_sql = """
        CREATE OR REPLACE VIEW product_search_view AS
        SELECT 
            p.id,
            p.name,
            p.description,
            p.image_url,
            p.plant_part_id,
            pp.name as plant_part_name,
            p.industry_sub_category_id,
            isc.name as industry_name,
            p.created_at,
            
            -- Company information
            STRING_AGG(DISTINCT hc.name, ', ') as company_names,
            COUNT(DISTINCT hcp.company_id) as company_count,
            
            -- Specifications aggregation
            COALESCE(
                json_agg(
                    DISTINCT jsonb_build_object(
                        'category', ps.spec_category,
                        'type', ps.spec_type,
                        'name', ps.spec_name,
                        'value', ps.typical_value,
                        'unit', ps.unit
                    )
                ) FILTER (WHERE ps.id IS NOT NULL),
                '[]'::json
            ) as specifications,
            
            -- TRL information
            t.trl_level,
            t.commercial_stage,
            t.moq_quantity,
            t.moq_unit,
            
            -- Search vectors for full-text search
            to_tsvector('english', 
                COALESCE(p.name, '') || ' ' || 
                COALESCE(p.description, '') || ' ' ||
                COALESCE(pp.name, '') || ' ' ||
                COALESCE(isc.name, '')
            ) as search_vector
            
        FROM uses_products p
        LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
        LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
        LEFT JOIN hemp_company_products hcp ON p.id = hcp.product_id
        LEFT JOIN hemp_companies hc ON hcp.company_id = hc.id
        LEFT JOIN product_specifications ps ON p.id = ps.product_id
        LEFT JOIN product_trl_stages t ON p.id = t.product_id
        GROUP BY 
            p.id, p.name, p.description, p.image_url, p.plant_part_id, pp.name,
            p.industry_sub_category_id, isc.name, p.created_at,
            t.trl_level, t.commercial_stage, t.moq_quantity, t.moq_unit;
        """
        
        # Company search view
        company_search_sql = """
        CREATE OR REPLACE VIEW company_search_view AS
        SELECT 
            c.id,
            c.name,
            c.description,
            c.website,
            c.primary_focus,
            c.company_type,
            c.headquarters,
            c.founded_year,
            
            -- Product information
            COUNT(DISTINCT hcp.product_id) as product_count,
            STRING_AGG(DISTINCT p.name, ', ' ORDER BY p.name) as product_names,
            STRING_AGG(DISTINCT pp.name, ', ') as plant_parts,
            STRING_AGG(DISTINCT isc.name, ', ') as industries,
            
            -- Search vector
            to_tsvector('english', 
                COALESCE(c.name, '') || ' ' || 
                COALESCE(c.description, '') || ' ' ||
                COALESCE(c.primary_focus, '') || ' ' ||
                COALESCE(c.company_type, '')
            ) as search_vector
            
        FROM hemp_companies c
        LEFT JOIN hemp_company_products hcp ON c.id = hcp.company_id
        LEFT JOIN uses_products p ON hcp.product_id = p.id
        LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
        LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
        GROUP BY 
            c.id, c.name, c.description, c.website, c.primary_focus,
            c.company_type, c.headquarters, c.founded_year;
        """
        
        # Specification aggregation view
        spec_aggregation_sql = """
        CREATE OR REPLACE VIEW specification_aggregation_view AS
        SELECT 
            spec_category,
            spec_type,
            spec_name,
            unit,
            COUNT(*) as product_count,
            MIN(typical_value) as min_value,
            AVG(typical_value) as avg_value,
            MAX(typical_value) as max_value,
            ARRAY_AGG(DISTINCT product_id) as product_ids
        FROM product_specifications
        WHERE typical_value IS NOT NULL
        GROUP BY spec_category, spec_type, spec_name, unit;
        """
        
        # Execute view creation
        views = [
            ("product_search_view", search_view_sql),
            ("company_search_view", company_search_sql),
            ("specification_aggregation_view", spec_aggregation_sql)
        ]
        
        for view_name, sql in views:
            try:
                # Use apply_migration for DDL operations
                from supabase._sync.client import create_client
                migration_name = f"create_{view_name}"
                result = self.supabase.rpc('exec_sql', {'sql': sql}).execute()
                logger.info(f"✅ Created view: {view_name}")
            except Exception as e:
                logger.error(f"❌ Error creating view {view_name}: {e}")
        
        return True
    
    def create_search_indexes(self):
        """Create search performance indexes"""
        logger.info("Creating search performance indexes...")
        
        indexes = [
            # Full-text search indexes
            "CREATE INDEX IF NOT EXISTS idx_products_search_gin ON uses_products USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')))",
            "CREATE INDEX IF NOT EXISTS idx_companies_search_gin ON hemp_companies USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')))",
            
            # Faceted search indexes
            "CREATE INDEX IF NOT EXISTS idx_products_plant_part ON uses_products(plant_part_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_industry ON uses_products(industry_sub_category_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_created_at ON uses_products(created_at)",
            
            # Specification search indexes
            "CREATE INDEX IF NOT EXISTS idx_specs_category ON product_specifications(spec_category)",
            "CREATE INDEX IF NOT EXISTS idx_specs_type ON product_specifications(spec_type)",
            "CREATE INDEX IF NOT EXISTS idx_specs_value ON product_specifications(typical_value) WHERE typical_value IS NOT NULL",
            
            # TRL indexes
            "CREATE INDEX IF NOT EXISTS idx_trl_level ON product_trl_stages(trl_level)",
            "CREATE INDEX IF NOT EXISTS idx_commercial_stage ON product_trl_stages(commercial_stage)",
            
            # Company relationship indexes
            "CREATE INDEX IF NOT EXISTS idx_company_products_product ON hemp_company_products(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_company_products_company ON hemp_company_products(company_id)",
        ]
        
        success_count = 0
        for index_sql in indexes:
            try:
                result = self.supabase.rpc('exec_sql', {'sql': index_sql}).execute()
                success_count += 1
                logger.info(f"✅ Created index: {index_sql.split('idx_')[1].split(' ')[0] if 'idx_' in index_sql else 'unnamed'}")
            except Exception as e:
                logger.error(f"❌ Error creating index: {e}")
        
        logger.info(f"Created {success_count}/{len(indexes)} indexes successfully")
        return success_count
    
    def create_search_functions(self):
        """Create search utility functions"""
        logger.info("Creating search utility functions...")
        
        # Advanced product search function
        advanced_search_sql = """
        CREATE OR REPLACE FUNCTION advanced_product_search(
            search_query text DEFAULT '',
            plant_parts integer[] DEFAULT NULL,
            industries integer[] DEFAULT NULL,
            trl_min integer DEFAULT 1,
            trl_max integer DEFAULT 9,
            has_specs boolean DEFAULT NULL,
            limit_count integer DEFAULT 50,
            offset_count integer DEFAULT 0
        )
        RETURNS TABLE (
            id integer,
            name text,
            description text,
            plant_part_name text,
            industry_name text,
            company_count bigint,
            trl_level integer,
            commercial_stage text,
            rank real
        )
        LANGUAGE plpgsql
        AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                psv.id,
                psv.name,
                psv.description,
                psv.plant_part_name,
                psv.industry_name,
                psv.company_count,
                psv.trl_level,
                psv.commercial_stage,
                CASE 
                    WHEN search_query != '' THEN ts_rank(psv.search_vector, plainto_tsquery('english', search_query))
                    ELSE 1.0
                END as rank
            FROM product_search_view psv
            WHERE (
                search_query = '' OR psv.search_vector @@ plainto_tsquery('english', search_query)
            )
            AND (plant_parts IS NULL OR psv.plant_part_id = ANY(plant_parts))
            AND (industries IS NULL OR psv.industry_sub_category_id = ANY(industries))
            AND (psv.trl_level IS NULL OR psv.trl_level BETWEEN trl_min AND trl_max)
            AND (has_specs IS NULL OR 
                 (has_specs = true AND psv.specifications != '[]'::json) OR
                 (has_specs = false AND psv.specifications = '[]'::json))
            ORDER BY rank DESC, psv.created_at DESC
            LIMIT limit_count
            OFFSET offset_count;
        END;
        $$;
        """
        
        # Facet counting function
        facet_count_sql = """
        CREATE OR REPLACE FUNCTION get_search_facets(
            search_query text DEFAULT ''
        )
        RETURNS json
        LANGUAGE plpgsql
        AS $$
        DECLARE
            result json;
        BEGIN
            WITH search_results AS (
                SELECT id FROM product_search_view psv
                WHERE search_query = '' OR psv.search_vector @@ plainto_tsquery('english', search_query)
            ),
            plant_part_counts AS (
                SELECT 
                    pp.id,
                    pp.name,
                    COUNT(sr.id) as count
                FROM plant_parts pp
                LEFT JOIN uses_products p ON pp.id = p.plant_part_id
                LEFT JOIN search_results sr ON p.id = sr.id
                GROUP BY pp.id, pp.name
                HAVING COUNT(sr.id) > 0
            ),
            industry_counts AS (
                SELECT 
                    isc.id,
                    isc.name,
                    COUNT(sr.id) as count
                FROM industry_sub_categories isc
                LEFT JOIN uses_products p ON isc.id = p.industry_sub_category_id
                LEFT JOIN search_results sr ON p.id = sr.id
                GROUP BY isc.id, isc.name
                HAVING COUNT(sr.id) > 0
            )
            SELECT json_build_object(
                'plant_parts', COALESCE(json_agg(row_to_json(ppc.*)), '[]'::json),
                'industries', COALESCE(json_agg(row_to_json(ic.*)), '[]'::json)
            ) INTO result
            FROM plant_part_counts ppc, industry_counts ic;
            
            RETURN result;
        END;
        $$;
        """
        
        functions = [
            ("advanced_product_search", advanced_search_sql),
            ("get_search_facets", facet_count_sql)
        ]
        
        success_count = 0
        for func_name, sql in functions:
            try:
                result = self.supabase.rpc('exec_sql', {'sql': sql}).execute()
                success_count += 1
                logger.info(f"✅ Created function: {func_name}")
            except Exception as e:
                logger.error(f"❌ Error creating function {func_name}: {e}")
        
        logger.info(f"Created {success_count}/{len(functions)} functions successfully")
        return success_count
    
    def test_search_functionality(self):
        """Test the search infrastructure"""
        logger.info("Testing search functionality...")
        
        tests = []
        
        # Test 1: Basic product search
        try:
            result = self.supabase.rpc('advanced_product_search', {
                'search_query': 'hemp fiber',
                'limit_count': 5
            }).execute()
            tests.append(f"✅ Basic search: Found {len(result.data)} fiber products")
        except Exception as e:
            tests.append(f"❌ Basic search failed: {e}")
        
        # Test 2: Faceted search
        try:
            result = self.supabase.rpc('get_search_facets', {
                'search_query': 'construction'
            }).execute()
            tests.append(f"✅ Faceted search: Retrieved facet data")
        except Exception as e:
            tests.append(f"❌ Faceted search failed: {e}")
        
        # Test 3: View access
        try:
            result = self.supabase.table('product_search_view').select('id, name').limit(5).execute()
            tests.append(f"✅ Search view: Found {len(result.data)} products in view")
        except Exception as e:
            tests.append(f"❌ Search view failed: {e}")
        
        for test_result in tests:
            logger.info(test_result)
        
        return len([t for t in tests if "✅" in t])
    
    def setup_complete_infrastructure(self):
        """Run complete search infrastructure setup"""
        logger.info("Setting up complete search infrastructure...")
        
        # Step 1: Create views
        logger.info("Step 1: Creating search views...")
        self.create_search_views()
        
        # Step 2: Create indexes
        logger.info("Step 2: Creating search indexes...")
        index_count = self.create_search_indexes()
        
        # Step 3: Create functions
        logger.info("Step 3: Creating search functions...")
        func_count = self.create_search_functions()
        
        # Step 4: Test functionality
        logger.info("Step 4: Testing search functionality...")
        test_count = self.test_search_functionality()
        
        logger.info("\n" + "="*60)
        logger.info("SEARCH INFRASTRUCTURE SETUP COMPLETE")
        logger.info(f"✅ Views: Created search and aggregation views")
        logger.info(f"✅ Indexes: {index_count} performance indexes created")
        logger.info(f"✅ Functions: {func_count} search functions created")
        logger.info(f"✅ Tests: {test_count}/3 tests passed")
        logger.info("="*60)
        
        logger.info("\n🔍 Search Features Now Available:")
        logger.info("• Full-text search across products and companies")
        logger.info("• Faceted search by plant part and industry")
        logger.info("• TRL-level filtering for commercial readiness")
        logger.info("• Specification-based product filtering")
        logger.info("• Company-product relationship search")
        logger.info("• Advanced search functions with ranking")
        
        return True

def main():
    """Main entry point"""
    setup = SearchInfrastructureSetup()
    setup.setup_complete_infrastructure()

if __name__ == "__main__":
    main()