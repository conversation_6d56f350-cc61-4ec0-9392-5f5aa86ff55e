#!/usr/bin/env python3
"""
Clean orphaned references before merging duplicates
"""
import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

conn = psycopg2.connect(os.getenv('DATABASE_URL'))
cursor = conn.cursor()

print("🧹 CLEANING ORPHANED REFERENCES")
print("="*60)

# Tables that might have product references (order matters for FK constraints)
tables_to_clean = [
    ('image_generation_history', 'product_id'),
    ('image_generation_queue', 'product_id'),
    ('ai_generation_costs', 'product_id'),
]

for table, column in tables_to_clean:
    try:
        # Check if table exists
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_name = %s
        """, (table,))
        
        if cursor.fetchone()[0] > 0:
            # Delete orphaned records
            cursor.execute(f"""
                DELETE FROM {table}
                WHERE {column} NOT IN (SELECT id FROM uses_products)
            """)
            
            deleted = cursor.rowcount
            if deleted > 0:
                print(f"✅ Deleted {deleted} orphaned records from {table}")
            
            # For image generation queue, just empty it
            if table == 'image_generation_queue':
                cursor.execute(f"DELETE FROM {table}")
                print(f"   Cleared all {cursor.rowcount} records from {table}")
                
    except Exception as e:
        print(f"⚠️  Error cleaning {table}: {e}")
        conn.rollback()
        continue

conn.commit()
print("\n✅ Cleanup complete")
conn.close()