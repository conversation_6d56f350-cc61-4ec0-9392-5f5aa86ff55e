#!/usr/bin/env python3
"""
Automatically remove duplicates - keeps the best version
"""
import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def auto_remove_duplicates():
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    print("🔍 AUTO-REMOVING DUPLICATES")
    print("="*60)
    
    # Find duplicate groups
    cursor.execute("""
        SELECT LOWER(name) as name_lower, COUNT(*) as count, 
               array_agg(id ORDER BY 
                   COALESCE(data_completeness_score, 0) DESC, 
                   created_at ASC) as ids,
               array_agg(name ORDER BY 
                   COALESCE(data_completeness_score, 0) DESC, 
                   created_at ASC) as names,
               array_agg(COALESCE(data_completeness_score, 0) ORDER BY 
                   COALESCE(data_completeness_score, 0) DESC, 
                   created_at ASC) as scores
        FROM uses_products
        GROUP BY LOWER(name)
        HAVING COUNT(*) > 1
        ORDER BY count DESC, LOWER(name)
    """)
    
    duplicates = cursor.fetchall()
    
    if not duplicates:
        print("✅ No duplicates found!")
        return
    
    print(f"Found {len(duplicates)} groups with {sum(count - 1 for _, count, _, _, _ in duplicates)} total duplicates\n")
    
    total_removed = 0
    
    for name_lower, count, ids, names, scores in duplicates:
        # Keep the first one (highest score, oldest if tied)
        keep_id = ids[0]
        delete_ids = ids[1:]
        
        print(f"Processing '{names[0]}':")
        print(f"  Keeping: ID {keep_id} (score: {scores[0]})")
        print(f"  Removing: {len(delete_ids)} duplicates")
        
        try:
            # Delete duplicates
            for del_id in delete_ids:
                cursor.execute("DELETE FROM uses_products WHERE id = %s", (del_id,))
                total_removed += 1
            
            conn.commit()
            print(f"  ✅ Success\n")
            
        except Exception as e:
            conn.rollback()
            print(f"  ❌ Error: {e}\n")
    
    # Final summary
    print("\n📊 SUMMARY")
    print("="*60)
    print(f"Total duplicates removed: {total_removed}")
    
    # Check if any remain
    cursor.execute("""
        SELECT COUNT(*) FROM (
            SELECT name FROM uses_products 
            GROUP BY LOWER(name) 
            HAVING COUNT(*) > 1
        ) as dups
    """)
    remaining = cursor.fetchone()[0]
    
    if remaining > 0:
        print(f"⚠️  Duplicate groups remaining: {remaining}")
        print("Some duplicates could not be removed due to constraints")
    else:
        print("✅ All duplicates removed!")
    
    # Get new total
    cursor.execute("SELECT COUNT(*) FROM uses_products")
    total = cursor.fetchone()[0]
    print(f"Total products now: {total}")
    
    conn.close()

if __name__ == "__main__":
    auto_remove_duplicates()