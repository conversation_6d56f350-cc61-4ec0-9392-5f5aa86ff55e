#!/usr/bin/env python3
"""
Product validation module for AI agents
"""
import re
from difflib import SequenceMatcher

class ProductValidator:
    def __init__(self):
        self.prohibited_terms = [
            'cannabis leaf', 'marijuana', 'weed', 'ganja', 
            'thc', 'psychoactive', 'recreational cannabis'
        ]
        self.generic_phrases = [
            'hemp product', 'made from hemp', 
            'sustainable alternative', 'eco-friendly option'
        ]
    
    def validate_terminology(self, text):
        """Check for prohibited terms"""
        text_lower = text.lower()
        
        # Special case for garden weed context
        if 'weed' in text_lower and ('suppress' in text_lower or 'garden' in text_lower):
            return True, None
        
        for term in self.prohibited_terms:
            if term in text_lower:
                return False, f"Contains prohibited term: {term}"
        return True, None
    
    def validate_quality(self, product_data):
        """Validate product quality"""
        issues = []
        
        # Check description
        desc = product_data.get('description', '')
        if len(desc) < 100:
            issues.append("Description too short (min 100 chars)")
        
        # Check for generic phrases
        desc_lower = desc.lower()
        generic_count = sum(1 for phrase in self.generic_phrases if phrase in desc_lower)
        if generic_count >= 2:
            issues.append("Description too generic")
        
        # Check benefits
        benefits = product_data.get('benefits_advantages', [])
        if len(benefits) < 3:
            issues.append("Insufficient benefits (min 3)")
        
        return len(issues) == 0, issues
    
    def check_duplicate(self, name, existing_products):
        """Check for duplicates using fuzzy matching"""
        for existing in existing_products:
            similarity = SequenceMatcher(None, 
                                       name.lower(), 
                                       existing['name'].lower()).ratio()
            if similarity > 0.85:
                return True, existing['name'], similarity
        return False, None, 0
    
    def clean_text(self, text):
        """Clean text by replacing prohibited terms"""
        replacements = {
            'cannabis': 'hemp',
            'marijuana': 'industrial hemp',
            'weed': 'hemp',
            'psychoactive': 'non-psychoactive'
        }
        
        for old, new in replacements.items():
            text = re.sub(r'\b' + old + r'\b', new, text, flags=re.IGNORECASE)
        
        return text
