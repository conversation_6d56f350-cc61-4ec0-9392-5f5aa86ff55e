#!/usr/bin/env python3
"""
Remove final remaining duplicates
Interactive script to review and remove duplicates
"""
import os
import psycopg2
from dotenv import load_dotenv
from difflib import SequenceMatcher

load_dotenv()

def remove_duplicates():
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    print("🔍 FINDING REMAINING DUPLICATES")
    print("="*60)
    
    # Find duplicate groups
    cursor.execute("""
        SELECT LOWER(name) as name_lower, COUNT(*) as count, 
               array_agg(id ORDER BY created_at DESC) as ids,
               array_agg(name ORDER BY created_at DESC) as names,
               array_agg(data_completeness_score ORDER BY created_at DESC) as scores,
               array_agg(created_at ORDER BY created_at DESC) as dates
        FROM uses_products
        GROUP BY LOWER(name)
        HAVING COUNT(*) > 1
        ORDER BY count DESC, LOWER(name)
    """)
    
    duplicates = cursor.fetchall()
    
    if not duplicates:
        print("✅ No duplicates found!")
        return
    
    print(f"Found {len(duplicates)} groups with duplicates\n")
    
    total_removed = 0
    
    for i, (name_lower, count, ids, names, scores, dates) in enumerate(duplicates):
        print(f"\n[Group {i+1}/{len(duplicates)}] '{names[0]}' - {count} duplicates")
        print("-"*60)
        
        # Show all versions
        for j, (id, name, score, date) in enumerate(zip(ids, names, scores, dates)):
            print(f"{j+1}. ID: {id}")
            print(f"   Name: {name}")
            print(f"   Score: {score}")
            print(f"   Created: {date}")
            
            # Get additional info
            cursor.execute("""
                SELECT description, image_url, primary_company_id 
                FROM uses_products WHERE id = %s
            """, (id,))
            desc, img, company = cursor.fetchone()
            
            print(f"   Description: {desc[:100] + '...' if desc and len(desc) > 100 else desc}")
            print(f"   Has Image: {'✅' if img else '❌'}")
            print(f"   Has Company: {'✅' if company else '❌'}")
            print()
        
        # Auto-select best one
        best_idx = 0
        best_score = scores[0] or 0
        
        # Find the one with highest score and most data
        for j, (id, score) in enumerate(zip(ids, scores)):
            cursor.execute("""
                SELECT 
                    (CASE WHEN description IS NOT NULL AND LENGTH(description) > 50 THEN 1 ELSE 0 END) +
                    (CASE WHEN image_url IS NOT NULL THEN 1 ELSE 0 END) +
                    (CASE WHEN primary_company_id IS NOT NULL THEN 1 ELSE 0 END) as data_points,
                    COALESCE(data_completeness_score, 0) as score
                FROM uses_products WHERE id = %s
            """, (id,))
            data_points, product_score = cursor.fetchone()
            
            # Prefer higher score and more complete data
            if (product_score > best_score) or (product_score == best_score and j < best_idx):
                best_idx = j
                best_score = product_score
        
        print(f"🎯 Recommended to keep: #{best_idx + 1} (ID: {ids[best_idx]}, Score: {scores[best_idx]})")
        
        # Ask user
        action = input("\nAction? (k)eep recommended, (1-9) keep specific, (s)kip, (a)uto all, (q)uit: ").lower()
        
        if action == 'q':
            break
        elif action == 's':
            continue
        elif action == 'a':
            # Auto mode - keep best scoring one for all remaining
            keep_id = ids[best_idx]
            delete_ids = [id for id in ids if id != keep_id]
            
            # Delete the duplicates
            for del_id in delete_ids:
                cursor.execute("DELETE FROM uses_products WHERE id = %s", (del_id,))
                total_removed += 1
            
            conn.commit()
            print(f"✅ Kept ID {keep_id}, removed {len(delete_ids)} duplicates")
            
            # Process remaining automatically
            for remaining in duplicates[i+1:]:
                _, _, r_ids, r_names, r_scores, _ = remaining
                keep_idx = r_scores.index(max(s for s in r_scores if s is not None)) if any(r_scores) else 0
                keep_id = r_ids[keep_idx]
                delete_ids = [id for id in r_ids if id != keep_id]
                
                for del_id in delete_ids:
                    cursor.execute("DELETE FROM uses_products WHERE id = %s", (del_id,))
                    total_removed += 1
                
                print(f"✅ Auto-processed '{r_names[0]}': kept ID {keep_id}, removed {len(delete_ids)}")
            
            conn.commit()
            break
            
        elif action == 'k' or action == '':
            # Keep recommended
            keep_id = ids[best_idx]
            delete_ids = [id for id in ids if id != keep_id]
        elif action.isdigit() and 1 <= int(action) <= len(ids):
            # Keep specific one
            keep_idx = int(action) - 1
            keep_id = ids[keep_idx]
            delete_ids = [id for id in ids if id != keep_id]
        else:
            print("Invalid option, skipping...")
            continue
        
        # Delete the duplicates
        try:
            for del_id in delete_ids:
                cursor.execute("DELETE FROM uses_products WHERE id = %s", (del_id,))
                total_removed += 1
            
            conn.commit()
            print(f"✅ Kept ID {keep_id}, removed {len(delete_ids)} duplicates")
        except Exception as e:
            conn.rollback()
            print(f"❌ Error removing duplicates: {e}")
    
    # Final summary
    print(f"\n\n📊 SUMMARY")
    print("="*60)
    print(f"Total duplicates removed: {total_removed}")
    
    # Check remaining
    cursor.execute("""
        SELECT COUNT(*) FROM (
            SELECT name FROM uses_products 
            GROUP BY LOWER(name) 
            HAVING COUNT(*) > 1
        ) as dups
    """)
    remaining = cursor.fetchone()[0]
    print(f"Duplicate groups remaining: {remaining}")
    
    # Get new total
    cursor.execute("SELECT COUNT(*) FROM uses_products")
    total = cursor.fetchone()[0]
    print(f"Total products now: {total}")
    
    conn.close()

if __name__ == "__main__":
    remove_duplicates()