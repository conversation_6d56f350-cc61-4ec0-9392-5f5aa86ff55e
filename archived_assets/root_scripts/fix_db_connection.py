#!/usr/bin/env python3
"""
Fix database connection by forcing IPv4
"""

import socket
import os

# Force IPv4 for all connections
original_getaddrinfo = socket.getaddrinfo

def getaddrinfo_ipv4_only(host, port, family=0, type=0, proto=0, flags=0):
    # Force IPv4
    return original_getaddrinfo(host, port, socket.AF_INET, type, proto, flags)

# Monkey patch socket to use IPv4 only
socket.getaddrinfo = getaddrinfo_ipv4_only

print("Database connection fix applied - forcing IPv4")
print("You can now import this module before running agents:")
print("  import fix_db_connection")
print("  # then run your agent code")

# Also set an environment variable to help
os.environ['PGHOST_AF'] = 'inet'  # PostgreSQL specific IPv4 flag