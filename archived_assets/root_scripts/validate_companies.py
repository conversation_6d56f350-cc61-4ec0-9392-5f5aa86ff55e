#!/usr/bin/env python3
"""
Validate existing companies in the database using the Company Validation Pipeline
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import json
from supabase import create_client, Client
from dotenv import load_dotenv
from src.validation.company_validation_pipeline import CompanyValidationPipeline

# Load environment variables
load_dotenv()

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv("VITE_SUPABASE_URL"),
    os.getenv("SUPABASE_SERVICE_ROLE_KEY")
)

def fetch_all_companies():
    """Fetch all companies from database"""
    print("📊 Fetching companies from database...")
    
    response = supabase.table('hemp_companies').select("*").execute()
    companies = response.data
    
    print(f"✅ Found {len(companies)} companies")
    return companies

def update_company_quality_scores(validation_results):
    """Update companies with quality scores"""
    print("\n📝 Updating company quality scores...")
    
    updated_count = 0
    for result in validation_results:
        company_id = result['company_id']
        quality_score = result['quality_score'] / 100.0  # Convert to 0-1 scale
        
        try:
            # Update company with quality score
            supabase.table('hemp_companies').update({
                'quality_score': quality_score,
                'last_validated': datetime.now().isoformat(),
                'validation_notes': json.dumps({
                    'issues': result['issues'],
                    'warnings': result['warnings'],
                    'suggestions': result['suggestions']
                })
            }).eq('id', company_id).execute()
            
            updated_count += 1
        except Exception as e:
            print(f"❌ Error updating company {result['company_name']}: {e}")
            
    print(f"✅ Updated {updated_count} companies with quality scores")

def main():
    print("🏢 COMPANY VALIDATION PIPELINE")
    print("=" * 60)
    
    # Initialize pipeline
    pipeline = CompanyValidationPipeline()
    
    # Fetch companies
    companies = fetch_all_companies()
    if not companies:
        print("❌ No companies found in database")
        return
        
    # Run validation
    print("\n🔍 Running validation pipeline...")
    report = pipeline.generate_validation_report(companies)
    
    # Display results
    print("\n📊 VALIDATION RESULTS")
    print("=" * 60)
    print(f"Total Companies: {report['total_companies']}")
    print(f"Valid Companies: {report['valid_companies']}")
    print(f"Invalid Companies: {report['invalid_companies']}")
    print(f"Average Quality Score: {report['average_quality_score']:.1f}/100")
    
    # Show common issues
    if report['common_issues']:
        print("\n❌ Common Issues:")
        for issue, count in sorted(report['common_issues'].items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  - {issue}: {count} companies")
            
    # Show common warnings
    if report['common_warnings']:
        print("\n⚠️  Common Warnings:")
        for warning, count in sorted(report['common_warnings'].items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  - {warning}: {count} companies")
            
    # Show potential duplicates
    if report['potential_duplicates']:
        print(f"\n🔍 Found {len(report['potential_duplicates'])} potential duplicate pairs:")
        for dup in report['potential_duplicates'][:5]:
            print(f"  - {dup['company1']} ↔ {dup['company2']} (Similarity: {dup['similarity']:.0%})")
            
    # Show sample low-quality companies
    print("\n📉 Companies with Lowest Quality Scores:")
    low_quality = sorted(report['validation_results'], key=lambda x: x['quality_score'])[:5]
    for result in low_quality:
        print(f"\n{result['company_name']} (Score: {result['quality_score']:.0f}/100)")
        if result['issues']:
            print(f"  Issues: {', '.join(result['issues'][:2])}")
        if result['warnings']:
            print(f"  Warnings: {', '.join(result['warnings'][:2])}")
            
    # Show sample high-quality companies
    print("\n📈 Companies with Highest Quality Scores:")
    high_quality = sorted(report['validation_results'], key=lambda x: x['quality_score'], reverse=True)[:5]
    for result in high_quality:
        print(f"  - {result['company_name']} (Score: {result['quality_score']:.0f}/100)")
        
    # Save detailed report
    report_filename = f"company_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_filename, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"\n💾 Detailed report saved to: {report_filename}")
    
    # Ask if user wants to update quality scores
    response = input("\n🔄 Update company quality scores in database? (y/n): ")
    if response.lower() == 'y':
        update_company_quality_scores(report['validation_results'])
        print("\n✅ Company validation complete!")
    else:
        print("\n⏭️  Skipped updating quality scores")

if __name__ == "__main__":
    main()