#!/usr/bin/env python3
"""Fix truncated product names by extracting from descriptions"""
import os
import psycopg2
import re
from dotenv import load_dotenv

load_dotenv()
conn = psycopg2.connect(os.getenv('DATABASE_URL'))
cursor = conn.cursor()

print('🔧 FIXING TRUNCATED PRODUCT NAMES')
print('='*60)

# Manual mapping for common truncated names based on description patterns
name_fixes = {
    # CBD products
    1983: "CBD Infused Hydrating Facial Serum",
    1682: "CBD Rich Hemp Flower Tea Blends",
    
    # CBG products
    1715: "CBG Infused Sleep Patch",
    1981: "CBG Infused Topical Pain Relief Gel",
    
    # CBN products
    1970: "CBN Powered Relaxation Gummies",
    
    # Delta products
    1982: "Delta-8 THC Sleep Gummies",
    1716: "Delta-8 Hemp Extract Vegan Gummies",
    
    # Full Spectrum products
    2015: "Full Spectrum Hemp Hydration Powder",
    1717: "Full Spectrum CBD Topical Roll-On",
    
    # Hemp products
    1972: "Hemp Infused Sleep Sachets",
    1653: "Hemp Infused Sleep Sachets",
    1668: "Hemp Root Based Animal Bedding",
    
    # Nano products
    1980: "Nano-Emulsified CBD Energy Shots",
    1714: "Nano-Emulsified CBD Energy Gel",
    
    # Terpene products
    1684: "Terpene Enhanced Hemp Flower Vape Cartridges",
    1979: "Terpene Infused Sleep Gummies",
    
    # THC products
    1969: "THC-Free CBD Coffee Pods",
    1994: "THC-Free Focus Tincture",
    
    # Other fixes
    2025: "Hemp Hurd Bio-Based Polyurethane Foam",
    2024: "Hemp Leaf Protein-Fortified Energy Bars",
    1995: "Hemp Fiber-Reinforced Concrete Blocks",
    1990: "Hemp Root Composite Bioplastic",
    1988: "Hemp Root Biochar Fertilizer",
    1984: "Hemp Flower Cooking Spice Blend",
    2008: "CBG Focus Nasal Spray",
    2011: "CBG Focus Tincture",
    2013: "CBG Focus Booster Tincture",
    2019: "Hemp Root Biodegradable Packaging Foam",
    1999: "Hemp Leaf Protein Powder",
    2014: "THCV Metabolic Gel Caps",
    2030: "Hemp Shiv Pet Bedding",
    2026: "Hemp Hurd Grow Medium for Hydroponics"
}

# Apply fixes
fixed_count = 0
for product_id, new_name in name_fixes.items():
    cursor.execute(
        "UPDATE uses_products SET name = %s WHERE id = %s",
        (new_name, product_id)
    )
    if cursor.rowcount > 0:
        fixed_count += 1
        print(f"Fixed ID {product_id}: → {new_name}")

# Also fix any remaining very short names by extracting from description
cursor.execute("""
    SELECT id, name, description
    FROM uses_products 
    WHERE LENGTH(name) < 10
    AND id NOT IN %s
""", (tuple(name_fixes.keys()),))

auto_fixes = cursor.fetchall()
for id, name, desc in auto_fixes:
    if desc and ' - ' in desc:
        # Try to extract name from description pattern "Name - Description"
        potential_name = desc.split(' - ')[0].strip()
        if len(potential_name) > 10 and len(potential_name) < 100:
            cursor.execute(
                "UPDATE uses_products SET name = %s WHERE id = %s",
                (potential_name, id)
            )
            fixed_count += 1
            print(f"Auto-fixed ID {id}: {name} → {potential_name}")

conn.commit()
print(f"\n✅ Fixed {fixed_count} truncated product names")

# Verify
cursor.execute("""
    SELECT COUNT(*) FROM uses_products WHERE LENGTH(name) < 10
""")
remaining = cursor.fetchone()[0]
print(f"Remaining products with very short names: {remaining}")

conn.close()