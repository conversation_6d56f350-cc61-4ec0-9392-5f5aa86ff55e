#!/usr/bin/env python3
"""
Update database image URLs from absolute localhost URLs to relative paths
This allows images to work through Vite's proxy
"""
import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def update_to_relative_urls():
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    cursor = conn.cursor()
    
    # Update all localhost:3001 URLs to relative paths
    cursor.execute("""
        UPDATE uses_products 
        SET image_url = REPLACE(image_url, 'http://localhost:3001/', '/')
        WHERE image_url LIKE 'http://localhost:3001/%%'
    """)
    
    updated = cursor.rowcount
    conn.commit()
    
    print(f"✅ Updated {updated} image URLs to relative paths")
    
    # Show some examples
    cursor.execute("""
        SELECT id, name, image_url 
        FROM uses_products 
        WHERE image_url LIKE '/generated_images/%%'
        LIMIT 5
    """)
    
    print("\nExample updated URLs:")
    for row in cursor.fetchall():
        print(f"  ID {row[0]}: {row[1][:30]}... -> {row[2]}")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    update_to_relative_urls()