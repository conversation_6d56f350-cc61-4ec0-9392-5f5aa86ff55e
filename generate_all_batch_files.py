#!/usr/bin/env python3
"""
Generate ALL batch files for complete image update
"""
import json
import time

def load_all_generated_images():
    """Load all generated images from results file"""
    results = {}
    
    with open('logs/full_mcp_production_results.jsonl', 'r') as f:
        for line in f:
            if line.strip():
                result = json.loads(line.strip())
                product_id = result['product_id']
                image_url = result['image_url']
                results[product_id] = image_url
    
    return results

def create_batch_sql_file(batch, batch_num, total_batches):
    """Create a single batch SQL file"""
    # Build CASE statement
    case_statements = []
    product_ids = []
    
    for product_id, image_url in batch:
        safe_url = image_url.replace("'", "''")
        case_statements.append(f"WHEN {product_id} THEN '{safe_url}'")
        product_ids.append(str(product_id))
    
    # Create SQL
    sql = f"""-- Batch {batch_num}/{total_batches}
-- Products: {len(batch)}

UPDATE uses_products 
SET image_url = CASE id 
    {' '.join(case_statements)}
    ELSE image_url 
END
WHERE id IN ({','.join(product_ids)});"""
    
    # Save to file
    filename = f"batch_update_{batch_num:03d}.sql"
    with open(filename, 'w') as f:
        f.write(sql)
    
    return filename

def main():
    print("🚀 GENERATING ALL BATCH FILES FOR COMPLETE UPDATE")
    print("=" * 60)
    
    # Load all results
    results = load_all_generated_images()
    print(f"📁 Loaded {len(results)} generated images")
    
    # Create all batches (100 products each)
    product_items = list(results.items())
    batch_size = 100
    total_batches = (len(product_items) + batch_size - 1) // batch_size
    
    print(f"📦 Creating {total_batches} batch files...")
    
    created_files = []
    
    for i in range(total_batches):
        start_idx = i * batch_size
        end_idx = min(start_idx + batch_size, len(product_items))
        batch = product_items[start_idx:end_idx]
        
        filename = create_batch_sql_file(batch, i + 1, total_batches)
        created_files.append(filename)
        
        if (i + 1) % 10 == 0:
            print(f"   📄 Created {i + 1}/{total_batches} batch files...")
    
    print(f"\n✅ SUCCESS! Created {len(created_files)} batch files")
    print(f"📊 Total products to update: {len(results)}")
    print(f"💰 Total cost for all images: $14.15")
    
    print(f"\n🔧 EXECUTION PLAN:")
    print(f"   Execute batch files 001-048 via MCP Supabase")
    print(f"   Each batch updates ~100 products")
    print(f"   All {len(results)} placeholder images will be replaced")
    
    print(f"\n📈 EXPECTED RESULT:")
    print(f"   🖼️  All products will show AI-generated images")
    print(f"   🚫 No more placeholder/fallback images")
    print(f"   ✨ Professional hemp product visuals throughout")

if __name__ == "__main__":
    main()