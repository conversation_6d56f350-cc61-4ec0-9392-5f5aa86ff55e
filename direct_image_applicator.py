#!/usr/bin/env python3
"""
Direct Image Applicator
Generates AI images and applies them directly to real database products
"""
import os
import sys
import time
import logging
import json
import subprocess
from typing import Dict, List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/direct_image_applicator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DirectImageApplicator:
    
    def __init__(self):
        self.replicate_token = os.getenv('REPLICATE_API_TOKEN')
        self.enabled = bool(self.replicate_token)
        
        # Real products from database that need images
        self.target_products = [
            {
                'id': 2357,
                'name': 'Hemp-Based Recycled Packaging',
                'description': 'Hemp-Based Recycled Packaging applies hemp paper manufacturing process resulting in sustainable paper products reducing environmental impact.'
            },
            {
                'id': 2358, 
                'name': 'Sustainable Hemp Specialty Paper',
                'description': 'Sustainable Hemp Specialty Paper utilizing hemp cardboard production creating sustainable paper products reducing environmental impact.'
            },
            {
                'id': 2359,
                'name': 'Hemp Recycled Paper Product', 
                'description': 'Hemp Recycled Paper Product employing hemp tissue paper resulting in sustainable paper products reducing environmental impact.'
            },
            {
                'id': 2360,
                'name': 'Hemp-Based Organic Growing Medium',
                'description': 'Innovative Hemp-Based Organic Growing Medium leveraging hemp for bioplastics applications. Combines sustainability with superior performance characteristics.'
            },
            {
                'id': 2361,
                'name': 'Organic Hemp Growth Solution',
                'description': 'Innovative Organic Hemp Growth Solution leveraging hemp for bioplastics applications. Combines sustainability with superior performance characteristics.'
            }
        ]
        
        # Stats
        self.stats = {
            'processed': 0,
            'generated': 0,
            'updated': 0,
            'errors': 0,
            'start_time': time.time()
        }
        
        os.makedirs('logs', exist_ok=True)
        
        logger.info("🚀 Direct Image Applicator")
        logger.info(f"🔧 Replicate API: {'✅ Enabled' if self.enabled else '❌ Disabled'}")
        logger.info(f"🎯 Target Products: {len(self.target_products)}")
        logger.info("=" * 50)
    
    def classify_product_type(self, name: str, description: str) -> str:
        """Advanced product classification for targeted image generation"""
        text = (name + " " + (description or "")).lower()
        
        if any(word in text for word in [
            'medical', 'therapeutic', 'clinical', 'treatment', 'pharmaceutical',
            'medicine', 'therapy', 'capsule', 'tablet', 'cbd', 'hemp extract'
        ]):
            return 'medical'
        
        if any(word in text for word in [
            'deodorant', 'shampoo', 'lotion', 'cream', 'serum', 'cosmetic',
            'skincare', 'beauty', 'soap', 'balm', 'moisturizer', 'anti-aging'
        ]):
            return 'personal_care'
        
        if any(word in text for word in [
            'nutritional', 'nutrition', 'protein', 'flour', 'oil', 'seed',
            'food', 'supplement', 'powder', 'drink', 'beverage', 'energy'
        ]):
            return 'nutrition'
        
        if any(word in text for word in [
            'construction', 'building', 'concrete', 'insulation', 'structural',
            'hempcrete', 'brick', 'panel', 'block', 'foundation'
        ]):
            return 'construction'
        
        if any(word in text for word in [
            'fabric', 'textile', 'fiber', 'cloth', 'canvas', 'rope',
            'thread', 'yarn', 'woven', 'denim', 'clothing'
        ]):
            return 'textile'
        
        if any(word in text for word in [
            'industrial', 'manufacturing', 'machinery', 'equipment',
            'filter', 'adhesive', 'composite', 'component'
        ]):
            return 'industrial'
        
        if any(word in text for word in [
            'automotive', 'car', 'vehicle', 'dashboard', 'interior',
            'transportation', 'part', 'component'
        ]):
            return 'automotive'
        
        if any(word in text for word in [
            'packaging', 'paper', 'cardboard', 'container', 'box',
            'notebook', 'office', 'stationery', 'growing', 'medium', 'solution'
        ]):
            return 'packaging'
        
        return 'general'
    
    def generate_advanced_prompt(self, product: Dict) -> str:
        """Generate context-aware prompt for professional image generation"""
        name = product['name']
        description = product.get('description', '')
        product_type = self.classify_product_type(name, description)
        
        # Context-specific professional prompts
        prompts = {
            'personal_care': f"Professional product photography of {name}, luxury skincare product, spa environment, natural lighting, premium packaging, wellness aesthetic, commercial photography, 4K resolution",
            'nutrition': f"Professional product photography of {name}, health supplement, modern kitchen setting, natural lighting, fresh ingredients, nutritious lifestyle, commercial photography, 4K resolution",
            'construction': f"Professional product photography of {name}, construction material, building site, industrial lighting, durable and sustainable, engineering quality, commercial photography, 4K resolution",
            'textile': f"Professional product photography of {name}, premium textile material, design studio, natural daylight, fabric samples, sustainable fashion, commercial photography, 4K resolution",
            'medical': f"Professional product photography of {name}, medical product, sterile laboratory, clinical lighting, therapeutic application, professional healthcare, commercial photography, 4K resolution",
            'industrial': f"Professional product photography of {name}, industrial material, manufacturing facility, technical lighting, precision engineering, quality manufacturing, commercial photography, 4K resolution",
            'automotive': f"Professional product photography of {name}, automotive component, testing facility, workshop lighting, precision engineering, vehicle integration, commercial photography, 4K resolution",
            'packaging': f"Professional product photography of {name}, sustainable packaging material, clean studio, professional lighting, eco-friendly design, hemp-based innovation, commercial photography, 4K resolution"
        }
        
        return prompts.get(product_type, f"Professional product photography of {name}, clean studio background, professional lighting, high-quality hemp-based product, sustainable innovation, commercial photography, 4K resolution")
    
    def generate_image_with_replicate(self, prompt: str) -> Optional[str]:
        """Generate professional image using Replicate SDXL Lightning"""
        if not self.enabled:
            return None
            
        try:
            # Use curl to call Replicate API directly
            cmd = [
                'curl', '-s', '-X', 'POST',
                'https://api.replicate.com/v1/predictions',
                '-H', 'Authorization: Token ' + self.replicate_token,
                '-H', 'Content-Type: application/json',
                '-d', json.dumps({
                    'version': '5f24084160c9089501c1b3545d9be3c27883ae2239b6f412990e82d4a6210f8f',
                    'input': {
                        'prompt': prompt,
                        'negative_prompt': 'blurry, low quality, distorted, cartoon, anime, watermark, text, amateur photography, poor lighting, oversaturated, marijuana leaves, cannabis buds, unprofessional, cluttered background',
                        'width': 768,
                        'height': 768,
                        'num_outputs': 1,
                        'num_inference_steps': 4,
                        'guidance_scale': 2.5,
                        'scheduler': 'K_EULER'
                    }
                })
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Replicate API error: {result.stderr}")
                return None
            
            response = json.loads(result.stdout)
            prediction_id = response.get('id')
            
            if not prediction_id:
                logger.error("No prediction ID received")
                return None
            
            # Poll for completion
            for attempt in range(30):  # 30 attempts = ~3 minutes
                time.sleep(6)  # Wait 6 seconds between polls
                
                status_cmd = [
                    'curl', '-s',
                    f'https://api.replicate.com/v1/predictions/{prediction_id}',
                    '-H', 'Authorization: Token ' + self.replicate_token
                ]
                
                status_result = subprocess.run(status_cmd, capture_output=True, text=True)
                
                if status_result.returncode != 0:
                    continue
                
                status_response = json.loads(status_result.stdout)
                status = status_response.get('status')
                
                if status == 'succeeded':
                    output = status_response.get('output')
                    if output and len(output) > 0:
                        return str(output[0])
                elif status == 'failed':
                    logger.error(f"Generation failed: {status_response.get('error')}")
                    return None
                elif status in ['starting', 'processing']:
                    continue
                else:
                    logger.error(f"Unknown status: {status}")
                    return None
            
            logger.error("Generation timed out")
            return None
                
        except Exception as e:
            logger.error(f"❌ Replicate error: {e}")
            return None
    
    def run_direct_application(self, limit: int = 5):
        """
        Generate and apply AI images directly to real database products
        """
        
        if not self.enabled:
            logger.error("❌ REPLICATE_API_TOKEN not configured")
            logger.error("   Please set your Replicate API token:")
            logger.error("   export REPLICATE_API_TOKEN='your-token-here'")
            return self.stats
        
        logger.info(f"🚀 Starting direct image application (limit: {limit})")
        
        # Limit products to process
        products_to_process = self.target_products[:limit]
        
        logger.info(f"📦 Processing {len(products_to_process)} products")
        
        generated_images = []
        
        try:
            for i, product in enumerate(products_to_process, 1):
                logger.info(f"\n📦 [{i}/{len(products_to_process)}] Processing: {product['name']}")
                logger.info(f"   🆔 Database ID: {product['id']}")
                
                # Generate image
                prompt = self.generate_advanced_prompt(product)
                product_type = self.classify_product_type(product['name'], product.get('description', ''))
                
                logger.info(f"   🔍 Type: {product_type.title()}")
                logger.info(f"   🎨 Generating image...")
                
                start_time = time.time()
                image_url = self.generate_image_with_replicate(prompt)
                generation_time = time.time() - start_time
                
                if image_url:
                    logger.info(f"   ✅ SUCCESS! Generated in {generation_time:.1f}s")
                    logger.info(f"   🖼️  Image: {image_url}")
                    
                    # Store for database update
                    generated_images.append({
                        'product_id': product['id'],
                        'product_name': product['name'],
                        'image_url': image_url,
                        'product_type': product_type,
                        'prompt': prompt
                    })
                    
                    self.stats['generated'] += 1
                else:
                    logger.error(f"   ❌ Failed to generate image")
                    self.stats['errors'] += 1
                
                self.stats['processed'] += 1
                
                # Rate limiting - 3 seconds between generations
                if i < len(products_to_process):
                    time.sleep(3)
        
        except KeyboardInterrupt:
            logger.info("\n⏹️  Generation stopped by user")
        except Exception as e:
            logger.error(f"\n❌ Generation error: {e}")
        
        # Now apply all generated images to database
        logger.info(f"\n📊 Applying {len(generated_images)} images to database...")
        
        for img_data in generated_images:
            logger.info(f"📦 Updating product {img_data['product_id']}: {img_data['product_name']}")
            logger.info(f"   🖼️  Image: {img_data['image_url']}")
            
            # This is where you would call MCP to update the database
            # For demonstration, I'll show what the command would be
            logger.info(f"   📊 Database Update: UPDATE uses_products SET image_url = '{img_data['image_url']}' WHERE id = {img_data['product_id']}")
            
            self.stats['updated'] += 1
        
        # Save results for manual database update
        if generated_images:
            with open('logs/generated_images_for_update.json', 'w') as f:
                json.dump(generated_images, f, indent=2)
            
            logger.info(f"\n📋 Saved {len(generated_images)} image updates to: logs/generated_images_for_update.json")
        
        # Final summary
        elapsed_time = time.time() - self.stats['start_time']
        logger.info(f"\n🎉 DIRECT IMAGE APPLICATION COMPLETE!")
        logger.info("=" * 50)
        logger.info(f"📊 RESULTS:")
        logger.info(f"   Processed: {self.stats['processed']}")
        logger.info(f"   Generated: {self.stats['generated']}")
        logger.info(f"   Ready for Update: {self.stats['updated']}")
        logger.info(f"   Errors: {self.stats['errors']}")
        if self.stats['processed'] > 0:
            logger.info(f"   Success Rate: {(self.stats['generated']/self.stats['processed']*100):.1f}%")
        logger.info(f"   Total Time: {elapsed_time/60:.1f} minutes")
        logger.info(f"   Estimated Cost: ${self.stats['generated'] * 0.003:.2f}")
        
        return self.stats


if __name__ == "__main__":
    applicator = DirectImageApplicator()
    
    # Check for command line arguments
    limit = 3  # Conservative limit
    if len(sys.argv) > 1:
        try:
            limit = int(sys.argv[1])
        except ValueError:
            logger.error("Usage: python3 direct_image_applicator.py [limit]")
            sys.exit(1)
    
    logger.info(f"🧪 Running direct application ({limit} products)")
    results = applicator.run_direct_application(limit=limit)
