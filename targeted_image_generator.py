#!/usr/bin/env python3
"""
Targeted Image Generator
Generates accurate AI images for specific product categories
"""
import os
import sys
import time
import logging
import json
import subprocess
from typing import Dict, List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TargetedImageGenerator:
    
    def __init__(self):
        self.replicate_token = os.getenv('REPLICATE_API_TOKEN')
        self.enabled = bool(self.replicate_token)
        
        logger.info("🎯 Targeted Image Generator")
        logger.info(f"🔧 Replicate API: {'✅ Enabled' if self.enabled else '❌ Disabled'}")
    
    def generate_specialized_prompt(self, product: Dict) -> str:
        """Generate highly specialized prompts based on product analysis"""
        name = product['name'].lower()
        description = product.get('description', '').lower()
        
        # Agricultural Products
        if 'agricultural' in name:
            if 'growth' in name:
                return "Professional product photography of hemp-based agricultural growth products, organic farming supplements, plant growth enhancers, agricultural laboratory setting, professional lighting, commercial photography, 4K resolution"
            elif 'protection' in name:
                return "Professional product photography of hemp-based plant protection products, organic pesticides and herbicides, agricultural protection bottles and containers, farm laboratory setting, professional lighting, commercial photography, 4K resolution"
            elif 'organic' in name:
                return "Professional product photography of organic hemp agricultural products, sustainable farming materials, organic certification labels, agricultural warehouse setting, professional lighting, commercial photography, 4K resolution"
        
        # Growing Medium Products  
        if 'growing medium' in name:
            if 'growth' in name:
                return "Professional product photography of hemp-based growing medium for plant growth, organic potting soil and substrate, gardening bags and containers, greenhouse environment, professional lighting, commercial photography, 4K resolution"
            elif 'protection' in name:
                return "Professional product photography of protective hemp growing medium, plant protection substrate, specialized gardening soil, agricultural research facility, professional lighting, commercial photography, 4K resolution"
        
        # Electronic Components
        if 'electronic' in name or 'component' in name:
            return "Professional product photography of hemp-based electronic components, eco-friendly circuit boards and substrates, sustainable electronics materials, technology laboratory setting, professional lighting, commercial photography, 4K resolution"
        
        # Solutions
        if 'solution' in name:
            if 'organic' in name:
                return "Professional product photography of organic hemp solution bottles, liquid plant nutrients and supplements, laboratory grade containers, research facility setting, professional lighting, commercial photography, 4K resolution"
            elif 'protection' in name:
                return "Professional product photography of hemp protection solution, plant care liquid products, agricultural chemical bottles, farm supply setting, professional lighting, commercial photography, 4K resolution"
        
        # Default hemp product
        return f"Professional product photography of {product['name']}, sustainable hemp-based product, clean studio background, professional lighting, commercial photography, 4K resolution"
    
    def generate_image_with_replicate(self, prompt: str) -> Optional[str]:
        """Generate professional image using Replicate SDXL Lightning"""
        if not self.enabled:
            return None
            
        try:
            cmd = [
                'curl', '-s', '-X', 'POST',
                'https://api.replicate.com/v1/predictions',
                '-H', 'Authorization: Token ' + self.replicate_token,
                '-H', 'Content-Type: application/json',
                '-d', json.dumps({
                    'version': '5f24084160c9089501c1b3545d9be3c27883ae2239b6f412990e82d4a6210f8f',
                    'input': {
                        'prompt': prompt,
                        'negative_prompt': 'blurry, low quality, distorted, cartoon, anime, watermark, text, amateur photography, poor lighting, oversaturated, marijuana leaves, cannabis buds, unprofessional, cluttered background',
                        'width': 768,
                        'height': 768,
                        'num_outputs': 1,
                        'num_inference_steps': 4,
                        'guidance_scale': 2.5,
                        'scheduler': 'K_EULER'
                    }
                })
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Replicate API error: {result.stderr}")
                return None
            
            response = json.loads(result.stdout)
            prediction_id = response.get('id')
            
            if not prediction_id:
                logger.error("No prediction ID received")
                return None
            
            # Poll for completion
            for attempt in range(30):
                time.sleep(6)
                
                status_cmd = [
                    'curl', '-s',
                    f'https://api.replicate.com/v1/predictions/{prediction_id}',
                    '-H', 'Authorization: Token ' + self.replicate_token
                ]
                
                status_result = subprocess.run(status_cmd, capture_output=True, text=True)
                
                if status_result.returncode != 0:
                    continue
                
                status_response = json.loads(status_result.stdout)
                status = status_response.get('status')
                
                if status == 'succeeded':
                    output = status_response.get('output')
                    if output and len(output) > 0:
                        return str(output[0])
                elif status == 'failed':
                    logger.error(f"Generation failed: {status_response.get('error')}")
                    return None
                elif status in ['starting', 'processing']:
                    continue
                else:
                    logger.error(f"Unknown status: {status}")
                    return None
            
            logger.error("Generation timed out")
            return None
                
        except Exception as e:
            logger.error(f"❌ Replicate error: {e}")
            return None
    
    def process_batch(self, products: List[Dict]) -> List[Dict]:
        """Process a batch of products and generate targeted images"""
        results = []
        
        for i, product in enumerate(products, 1):
            logger.info(f"\n📦 [{i}/{len(products)}] Processing: {product['name']}")
            logger.info(f"   🆔 ID: {product['id']}")
            
            # Generate specialized prompt
            prompt = self.generate_specialized_prompt(product)
            logger.info(f"   🎨 Specialized prompt created")
            
            # Generate image
            start_time = time.time()
            image_url = self.generate_image_with_replicate(prompt)
            generation_time = time.time() - start_time
            
            if image_url:
                logger.info(f"   ✅ SUCCESS! Generated in {generation_time:.1f}s")
                logger.info(f"   🖼️  Image: {image_url}")
                
                results.append({
                    'id': product['id'],
                    'name': product['name'], 
                    'image_url': image_url,
                    'prompt': prompt
                })
            else:
                logger.error(f"   ❌ Failed to generate image")
            
            # Rate limiting
            if i < len(products):
                time.sleep(3)
        
        return results


if __name__ == "__main__":
    generator = TargetedImageGenerator()
    
    # Sample products that need targeted images
    sample_products = [
        {
            'id': 2362,
            'name': 'Hemp Growth Agricultural Product',
            'description': 'Authentic Hemp Growth Agricultural Product made from premium hemp hemp. Honors traditional bioplastics methods while meeting contemporary needs.'
        },
        {
            'id': 2363,
            'name': 'Hemp Protection Agricultural Product', 
            'description': 'Heritage Hemp Protection Agricultural Product showcasing the historical use of hemp hemp in bioplastics. Combines traditional craftsmanship with modern quality standards.'
        },
        {
            'id': 2364,
            'name': 'Hemp Organic Agricultural Product',
            'description': 'Traditional Hemp Organic Agricultural Product crafted from hemp hemp using time-honored techniques. This cultural artifact represents centuries of knowledge in bioplastics applications.'
        },
        {
            'id': 2365,
            'name': 'Hemp-Based Growth Growing Medium',
            'description': 'Cutting-edge Hemp-Based Growth Growing Medium featuring hemp hemp technology. Developed for bioplastics applications requiring sustainable alternatives.'
        },
        {
            'id': 2366,
            'name': 'Hemp-Based Protection Growing Medium',
            'description': 'Advanced Hemp-Based Protection Growing Medium made from premium hemp hemp. Designed for bioplastics use with focus on quality and environmental responsibility.'
        }
    ]
    
    # Process batch
    logger.info(f"🚀 Processing {len(sample_products)} targeted products")
    results = generator.process_batch(sample_products)
    
    # Save results
    if results:
        with open('logs/targeted_image_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"\n📋 Generated {len(results)} targeted images")
        logger.info("\n🖼️  RESULTS:")
        for result in results:
            logger.info(f"   {result['name']}: {result['image_url']}")
    else:
        logger.info("No images generated")
