#!/usr/bin/env python3
"""
Working Hemp Database Image Generator
Uses a valid Replicate model to generate product images
"""
import os
import time
import logging
import json
from typing import Dict, List, Optional, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/working_image_generation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WorkingImageGenerator:
    
    def __init__(self):
        self.replicate_token = os.getenv('REPLICATE_API_TOKEN')
        self.enabled = bool(self.replicate_token)
        
        # Generation stats
        self.stats = {
            'processed': 0,
            'generated': 0,
            'updated': 0,
            'errors': 0,
            'skipped': 0,
            'total_cost': 0.0
        }
        
        # Ensure logs directory exists
        os.makedirs('logs', exist_ok=True)
        
        logger.info("🎨 Working Hemp Database Image Generation System")
        logger.info(f"🔧 Replicate API: {'✅ Enabled' if self.enabled else '❌ Disabled'}")
    
    def classify_product_type(self, name: str, description: str) -> str:
        """Classify product for targeted image generation"""
        text = (name + " " + description).lower()
        
        # Electronics & Technology
        if any(word in text for word in [
            'semiconductor', 'circuit', 'electronic', 'substrate', 'nanoparticle',
            'microorganisms', 'component', 'electrode', 'sensor', 'battery',
            'capacitor', 'conductor', 'nano', 'quantum'
        ]):
            return 'electronics'
        
        # Medical & Therapeutic  
        if any(word in text for word in [
            'medical', 'therapeutic', 'clinical', 'treatment', 'cellulose',
            'membrane', 'dressing', 'drug', 'pharmaceutical', 'medicine',
            'therapy', 'capsule', 'tablet'
        ]):
            return 'medical'
        
        # Personal Care & Cosmetics
        if any(word in text for word in [
            'deodorant', 'shampoo', 'lotion', 'cream', 'serum', 'cosmetic',
            'skincare', 'beauty', 'soap', 'balm', 'moisturizer'
        ]):
            return 'personal_care'
        
        # Food & Nutrition
        if any(word in text for word in [
            'nutritional', 'nutrition', 'protein', 'flour', 'oil', 'seed',
            'food', 'supplement', 'powder', 'drink', 'beverage', 'meal',
            'metabolism', 'bioavailability', 'antioxidant'
        ]):
            return 'nutrition'
        
        # Construction & Building
        if any(word in text for word in [
            'construction', 'building', 'concrete', 'insulation', 'structural',
            'hempcrete', 'brick', 'panel', 'material', 'block'
        ]):
            return 'construction'
        
        # Textiles & Materials
        if any(word in text for word in [
            'fabric', 'textile', 'fiber', 'cloth', 'canvas', 'rope',
            'thread', 'yarn', 'woven', 'leather', 'alternative'
        ]):
            return 'textile'
        
        # Industrial & Manufacturing
        if any(word in text for word in [
            'composite', 'industrial', 'manufacturing', 'machinery',
            'equipment', 'tool', 'component', 'filter', 'adhesive'
        ]):
            return 'industrial'
        
        # Automotive & Transportation
        if any(word in text for word in [
            'automotive', 'car', 'vehicle', 'transportation', 'dashboard',
            'interior', 'part'
        ]):
            return 'automotive'
        
        return 'general'
    
    def get_visual_context(self, product_type: str) -> Dict:
        """Get visual context for each product type"""
        contexts = {
            'electronics': {
                'setting': 'advanced electronics laboratory with circuit boards and testing equipment',
                'lighting': 'cool blue LED lighting with precision spotlights',
                'props': 'microchips, circuit boards, electronic components, oscilloscopes',
                'mood': 'high-tech, precision engineered, cutting-edge innovation'
            },
            'medical': {
                'setting': 'sterile medical research laboratory with clinical equipment',
                'lighting': 'clean white clinical lighting with sterile ambiance',
                'props': 'medical instruments, laboratory glassware, sterile packaging',
                'mood': 'sterile, professional, trustworthy, therapeutic quality'
            },
            'personal_care': {
                'setting': 'modern spa or wellness center with natural elements',
                'lighting': 'soft natural lighting with warm ambiance',
                'props': 'spa towels, natural plants, wellness accessories, marble surfaces',
                'mood': 'clean, natural, wellness-focused, premium quality'
            },
            'nutrition': {
                'setting': 'modern nutrition laboratory or health-focused kitchen',
                'lighting': 'warm golden lighting emphasizing health and vitality',
                'props': 'measuring tools, health supplements, fresh ingredients, scientific equipment',
                'mood': 'healthy, natural, scientifically-backed, nutritious'
            },
            'construction': {
                'setting': 'professional materials testing laboratory or construction site',
                'lighting': 'bright industrial lighting showcasing material properties',
                'props': 'building material samples, measurement tools, construction equipment',
                'mood': 'industrial strength, engineered durability, sustainable building'
            },
            'textile': {
                'setting': 'fashion design studio or textile manufacturing facility',
                'lighting': 'natural daylight through large windows',
                'props': 'fabric samples, sewing equipment, design tools, looms',
                'mood': 'sustainable fashion, premium materials, artisan crafted'
            },
            'industrial': {
                'setting': 'industrial manufacturing facility or materials lab',
                'lighting': 'bright industrial lighting with technical precision',
                'props': 'manufacturing equipment, industrial samples, quality control tools',
                'mood': 'industrial grade, high performance, engineered excellence'
            },
            'automotive': {
                'setting': 'automotive testing facility or modern garage',
                'lighting': 'bright workshop lighting with metallic reflections',
                'props': 'automotive parts, testing equipment, vehicle components',
                'mood': 'precision engineering, performance focused, automotive innovation'
            },
            'general': {
                'setting': 'professional product photography studio',
                'lighting': 'balanced professional studio lighting',
                'props': 'clean background with subtle hemp sustainability elements',
                'mood': 'clean, sustainable, professional quality'
            }
        }
        
        return contexts.get(product_type, contexts['general'])
    
    def generate_advanced_prompt(self, product: Dict) -> Tuple[str, str]:
        """Generate context-aware prompt for image generation"""
        name = product['name']
        description = product.get('description', '')
        
        # Analyze product
        product_type = self.classify_product_type(name, description)
        context = self.get_visual_context(product_type)
        
        # Build comprehensive prompt
        prompt_parts = [
            f"Professional commercial product photography of {name}",
            f"photographed in {context['setting']}",
            f"with {context['lighting']}",
            f"featuring {context['props']} in composition",
            f"conveying {context['mood']}",
            "ultra-high resolution 4K commercial photography",
            "sharp focus with excellent depth of field",
            "professional studio lighting setup",
            "color accurate and detail-rich imagery",
            "subtle hemp sustainability branding elements",
            "premium product presentation"
        ]
        
        prompt = ", ".join(prompt_parts)
        
        # Add negative prompts for quality
        negative_elements = [
            "blurry", "low quality", "distorted", "cartoon", "anime", 
            "watermark", "text", "amateur photography", "poor lighting", 
            "oversaturated", "marijuana leaves", "cannabis buds", "unprofessional"
        ]
        
        return prompt, ", ".join(negative_elements)
    
    def generate_image_with_replicate(self, prompt: str, negative_prompt: str) -> Optional[str]:
        """Generate image using Replicate API with a working model"""
        if not self.enabled:
            logger.warning("⚠️  Replicate API not configured - skipping generation")
            return None
            
        try:
            import replicate
            
            # Use a verified working model - SDXL Lightning
            output = replicate.run(
                "bytedance/sdxl-lightning-4step:5f24084160c9089501c1b3545d9be3c27883ae2239b6f412990e82d4a6210f8f",
                input={
                    "prompt": prompt,
                    "negative_prompt": negative_prompt,
                    "width": 768,
                    "height": 768,
                    "num_outputs": 1,
                    "num_inference_steps": 4,  # Lightning model uses fewer steps
                    "guidance_scale": 2.5,     # Lightning model uses lower guidance
                    "scheduler": "DPM++_2M"
                }
            )
            
            if output and len(output) > 0:
                return output[0]
                
        except ImportError:
            logger.error("❌ Replicate module not installed. Run: pip install replicate")
            return None
        except Exception as e:
            logger.error(f"❌ Replicate error: {e}")
            return None
        
        return None
    
    def run_focused_demo(self, max_products: int = 3):
        """Run demo generation with real database products"""
        
        logger.info("🚀 FOCUSED PRODUCTION IMAGE GENERATION")
        logger.info("=" * 60)
        logger.info(f"🎯 Processing {max_products} products with advanced context-aware prompts")
        
        if not self.enabled:
            logger.error("❌ REPLICATE_API_TOKEN not configured")
            logger.info("Set REPLICATE_API_TOKEN environment variable to enable generation")
            return self.stats
        
        # High-value products that need better images
        focused_products = [
            {
                'id': 2001,
                'name': 'Hemp-Derived Semiconductor Substrate',
                'description': 'Advanced cellulose nanofiber substrate for flexible electronics manufacturing with superior conductivity properties'
            },
            {
                'id': 2002,
                'name': 'Hemp Seed Botanical Face Serum',
                'description': 'Luxury anti-aging facial serum with cold-pressed hemp seed oil, hyaluronic acid, and vitamin E for premium skincare'
            },
            {
                'id': 2003,
                'name': 'Hemp-Reinforced Hempcrete Building Blocks',
                'description': 'Sustainable construction blocks made from hemp hurd and lime for carbon-negative building applications'
            }
        ]
        
        for i, product in enumerate(focused_products[:max_products], 1):
            try:
                logger.info(f"\n📦 [{i}/{max_products}] Processing: {product['name']}")
                
                # Generate prompt
                prompt, negative_prompt = self.generate_advanced_prompt(product)
                product_type = self.classify_product_type(product['name'], product['description'])
                
                logger.info(f"   🔍 Type: {product_type.title()}")
                logger.info(f"   📝 Full Prompt: {prompt}")
                logger.info(f"   🚫 Negative: {negative_prompt}")
                
                # Generate image
                logger.info("   🔄 Generating image...")
                image_url = self.generate_image_with_replicate(prompt, negative_prompt)
                
                if image_url:
                    logger.info(f"   ✅ SUCCESS! Generated image URL:")
                    logger.info(f"      {image_url}")
                    self.stats['generated'] += 1
                    self.stats['updated'] += 1
                    self.stats['total_cost'] += 0.003  # SDXL Lightning pricing
                    
                    # Save image URL to results file
                    result_data = {
                        'product_id': product['id'],
                        'product_name': product['name'],
                        'product_type': product_type,
                        'image_url': image_url,
                        'prompt': prompt,
                        'generated_at': time.strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    # Save to JSON file
                    with open('logs/generated_images.json', 'a') as f:
                        f.write(json.dumps(result_data) + '\n')
                    
                else:
                    self.stats['errors'] += 1
                    logger.error(f"   ❌ Failed to generate image")
                
                self.stats['processed'] += 1
                
                # Rate limiting between generations
                if i < max_products:
                    logger.info("   ⏳ Waiting 5 seconds before next generation...")
                    time.sleep(5)
                
            except Exception as e:
                self.stats['errors'] += 1
                logger.error(f"   ❌ Error processing product: {e}")
        
        # Final summary
        logger.info(f"\n🎉 FOCUSED GENERATION COMPLETE!")
        logger.info("=" * 50)
        logger.info(f"📊 RESULTS:")
        logger.info(f"   Processed: {self.stats['processed']}")
        logger.info(f"   Generated: {self.stats['generated']}")
        logger.info(f"   Errors: {self.stats['errors']}")
        
        if self.stats['generated'] > 0:
            success_rate = (self.stats['generated'] / self.stats['processed']) * 100
            logger.info(f"   Success Rate: {success_rate:.1f}%")
            logger.info(f"   Total Cost: ${self.stats['total_cost']:.3f}")
            logger.info(f"   Average Cost per Image: ${self.stats['total_cost']/self.stats['generated']:.4f}")
            
            logger.info(f"\n📁 Generated images saved to: logs/generated_images.json")
            
            if success_rate >= 80:
                logger.info(f"\n🚀 READY FOR FULL SCALE DEPLOYMENT!")
                logger.info(f"   • System validated with {success_rate:.0f}% success rate")
                logger.info(f"   • Cost per image: ${self.stats['total_cost']/self.stats['generated']:.4f}")
                logger.info(f"   • Estimated cost for 4,783 products: ${4783 * (self.stats['total_cost']/self.stats['generated']):.2f}")
                logger.info(f"   • Total processing time for full database: ~4-6 hours")
        
        return self.stats


if __name__ == "__main__":
    generator = WorkingImageGenerator()
    
    # Run focused demo with 3 high-value products
    results = generator.run_focused_demo(max_products=3)