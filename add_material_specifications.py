#!/usr/bin/env python3
"""
Add Material Specifications to Hemp Products
Extracts and adds technical specifications like fiber length, protein content, etc.
"""

import os
import re
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from supabase import create_client, Client
import asyncio
import aiohttp
from bs4 import BeautifulSoup
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class ProductSpec:
    product_id: int
    spec_category: str
    spec_type: str
    spec_name: str
    min_value: Optional[float]
    typical_value: Optional[float]
    max_value: Optional[float]
    unit: str
    test_method: Optional[str]
    source: str

class MaterialSpecificationExtractor:
    def __init__(self):
        self.supabase = self._init_supabase()
        self.plant_part_specs = self._load_plant_part_specifications()
        
    def _init_supabase(self) -> Client:
        """Initialize Supabase client"""
        url = os.getenv('VITE_SUPABASE_URL', 'https://ktoqznqmlnxrtvubewyz.supabase.co')
        key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        if not key:
            key = os.getenv('VITE_SUPABASE_ANON_KEY')
        return create_client(url, key)
    
    def _load_plant_part_specifications(self) -> Dict[str, List[Dict]]:
        """Define specifications by plant part"""
        return {
            'Bast Fiber': [
                {
                    'category': 'physical',
                    'type': 'fiber_length',
                    'name': 'Fiber Length',
                    'unit': 'mm',
                    'typical_range': (15, 40),
                    'test_method': 'ASTM D1440'
                },
                {
                    'category': 'mechanical',
                    'type': 'tensile_strength',
                    'name': 'Tensile Strength',
                    'unit': 'MPa',
                    'typical_range': (550, 900),
                    'test_method': 'ASTM D3822'
                },
                {
                    'category': 'physical',
                    'type': 'fineness',
                    'name': 'Fineness',
                    'unit': 'tex',
                    'typical_range': (15, 25),
                    'test_method': 'ASTM D1907'
                },
                {
                    'category': 'chemical',
                    'type': 'cellulose_content',
                    'name': 'Cellulose Content',
                    'unit': '%',
                    'typical_range': (68, 78),
                    'test_method': 'TAPPI T203'
                }
            ],
            'Hurd': [
                {
                    'category': 'physical',
                    'type': 'bulk_density',
                    'name': 'Bulk Density',
                    'unit': 'kg/m³',
                    'typical_range': (110, 150),
                    'test_method': 'EN 1097-3'
                },
                {
                    'category': 'physical',
                    'type': 'particle_size',
                    'name': 'Particle Size',
                    'unit': 'mm',
                    'typical_range': (5, 20),
                    'test_method': 'EN 933-1'
                },
                {
                    'category': 'thermal',
                    'type': 'thermal_conductivity',
                    'name': 'Thermal Conductivity',
                    'unit': 'W/m·K',
                    'typical_range': (0.06, 0.08),
                    'test_method': 'EN 12667'
                }
            ],
            'Seeds': [
                {
                    'category': 'nutritional',
                    'type': 'protein_content',
                    'name': 'Protein Content',
                    'unit': '%',
                    'typical_range': (20, 30),
                    'test_method': 'AOAC 990.03'
                },
                {
                    'category': 'nutritional',
                    'type': 'oil_content',
                    'name': 'Oil Content',
                    'unit': '%',
                    'typical_range': (25, 35),
                    'test_method': 'AOCS Am 2-93'
                },
                {
                    'category': 'physical',
                    'type': 'moisture_content',
                    'name': 'Moisture Content',
                    'unit': '%',
                    'typical_range': (6, 9),
                    'test_method': 'AOAC 925.10'
                }
            ],
            'Oil': [
                {
                    'category': 'chemical',
                    'type': 'omega_3_fatty_acids',
                    'name': 'Omega-3 Fatty Acids',
                    'unit': '%',
                    'typical_range': (15, 25),
                    'test_method': 'AOCS Ce 1h-05'
                },
                {
                    'category': 'chemical',
                    'type': 'omega_6_fatty_acids',
                    'name': 'Omega-6 Fatty Acids',
                    'unit': '%',
                    'typical_range': (50, 60),
                    'test_method': 'AOCS Ce 1h-05'
                },
                {
                    'category': 'physical',
                    'type': 'iodine_value',
                    'name': 'Iodine Value',
                    'unit': 'g I2/100g',
                    'typical_range': (145, 165),
                    'test_method': 'AOCS Cd 1-25'
                }
            ],
            'Flower': [
                {
                    'category': 'chemical',
                    'type': 'cbd_content',
                    'name': 'CBD Content',
                    'unit': '%',
                    'typical_range': (0.3, 20),
                    'test_method': 'USP <467>'
                },
                {
                    'category': 'chemical',
                    'type': 'thc_content',
                    'name': 'THC Content',
                    'unit': '%',
                    'typical_range': (0, 0.3),
                    'test_method': 'USP <467>'
                },
                {
                    'category': 'chemical',
                    'type': 'terpene_content',
                    'name': 'Total Terpenes',
                    'unit': '%',
                    'typical_range': (0.5, 3),
                    'test_method': 'GC-MS'
                }
            ]
        }
    
    def extract_specs_from_description(self, description: str, plant_part: str) -> List[ProductSpec]:
        """Extract specifications from product description"""
        specs = []
        
        if not description or not plant_part:
            return specs
        
        # Common patterns for extracting numeric values with units
        patterns = {
            'protein': r'(\d+(?:\.\d+)?)\s*%?\s*protein',
            'fiber_length': r'(\d+(?:\.\d+)?)\s*(mm|millimeter|inch|in)\s*(?:fiber\s*)?length',
            'tensile_strength': r'(\d+(?:\.\d+)?)\s*(mpa|psi)\s*(?:tensile\s*)?strength',
            'oil_content': r'(\d+(?:\.\d+)?)\s*%?\s*oil(?:\s*content)?',
            'moisture': r'(\d+(?:\.\d+)?)\s*%?\s*moisture',
            'density': r'(\d+(?:\.\d+)?)\s*(kg/m³|g/cm³)\s*(?:bulk\s*)?density',
            'particle_size': r'(\d+(?:\.\d+)?)\s*(mm|mesh)\s*(?:particle\s*)?size',
            'cbd': r'(\d+(?:\.\d+)?)\s*%?\s*cbd',
            'thc': r'(\d+(?:\.\d+)?)\s*%?\s*thc',
        }
        
        desc_lower = description.lower()
        
        for spec_type, pattern in patterns.items():
            matches = re.finditer(pattern, desc_lower, re.IGNORECASE)
            
            for match in matches:
                value = float(match.group(1))
                unit = match.group(2) if len(match.groups()) > 1 else self._get_default_unit(spec_type)
                
                # Map spec type to category
                category = self._get_spec_category(spec_type)
                
                spec = ProductSpec(
                    product_id=0,  # Will be set by caller
                    spec_category=category,
                    spec_type=spec_type,
                    spec_name=self._get_spec_name(spec_type),
                    min_value=None,
                    typical_value=value,
                    max_value=None,
                    unit=unit or '%',
                    test_method=None,
                    source='description_extraction'
                )
                specs.append(spec)
        
        return specs
    
    def add_standard_specs_by_plant_part(self, product_id: int, plant_part: str) -> List[ProductSpec]:
        """Add standard specifications based on plant part"""
        specs = []
        
        if plant_part not in self.plant_part_specs:
            return specs
        
        for spec_def in self.plant_part_specs[plant_part]:
            min_val, max_val = spec_def['typical_range']
            typical_val = (min_val + max_val) / 2
            
            spec = ProductSpec(
                product_id=product_id,
                spec_category=spec_def['category'],
                spec_type=spec_def['type'],
                spec_name=spec_def['name'],
                min_value=min_val,
                typical_value=typical_val,
                max_value=max_val,
                unit=spec_def['unit'],
                test_method=spec_def.get('test_method'),
                source='plant_part_standards'
            )
            specs.append(spec)
        
        return specs
    
    def _get_spec_category(self, spec_type: str) -> str:
        """Map specification type to category"""
        categories = {
            'protein': 'nutritional',
            'oil_content': 'nutritional',
            'moisture': 'physical',
            'fiber_length': 'physical',
            'tensile_strength': 'mechanical',
            'density': 'physical',
            'particle_size': 'physical',
            'cbd': 'chemical',
            'thc': 'chemical'
        }
        return categories.get(spec_type, 'physical')
    
    def _get_spec_name(self, spec_type: str) -> str:
        """Get human-readable specification name"""
        names = {
            'protein': 'Protein Content',
            'oil_content': 'Oil Content',
            'moisture': 'Moisture Content',
            'fiber_length': 'Fiber Length',
            'tensile_strength': 'Tensile Strength',
            'density': 'Bulk Density',
            'particle_size': 'Particle Size',
            'cbd': 'CBD Content',
            'thc': 'THC Content'
        }
        return names.get(spec_type, spec_type.replace('_', ' ').title())
    
    def _get_default_unit(self, spec_type: str) -> str:
        """Get default unit for specification type"""
        units = {
            'protein': '%',
            'oil_content': '%',
            'moisture': '%',
            'fiber_length': 'mm',
            'tensile_strength': 'MPa',
            'density': 'kg/m³',
            'particle_size': 'mm',
            'cbd': '%',
            'thc': '%'
        }
        return units.get(spec_type, '')
    
    def load_products_without_specs(self) -> List[Dict]:
        """Load products that don't have specifications yet"""
        logger.info("Loading products without specifications...")
        
        # Get products with plant part info
        products = self.supabase.table('uses_products').select(
            'id, name, description, plant_part_id'
        ).limit(2000).execute()
        
        # Get plant part names
        plant_parts = self.supabase.table('plant_parts').select('id, name').execute()
        plant_part_map = {pp['id']: pp['name'] for pp in plant_parts.data}
        
        # Get products that already have specs
        existing_specs = self.supabase.table('product_specifications').select('product_id').execute()
        products_with_specs = set(spec['product_id'] for spec in existing_specs.data) if existing_specs.data else set()
        
        # Filter products without specs
        products_without_specs = []
        for product in products.data:
            if product['id'] not in products_with_specs:
                product['plant_part_name'] = plant_part_map.get(product['plant_part_id'], 'Unknown')
                products_without_specs.append(product)
        
        logger.info(f"Found {len(products_without_specs)} products without specifications")
        return products_without_specs
    
    def save_specifications(self, specs: List[ProductSpec]) -> int:
        """Save specifications to database"""
        if not specs:
            return 0
        
        # First, check if product_specifications table exists
        try:
            # Try to insert specifications
            spec_data = []
            for spec in specs:
                spec_dict = {
                    'product_id': spec.product_id,
                    'spec_category': spec.spec_category,
                    'spec_type': spec.spec_type,
                    'spec_name': spec.spec_name,
                    'min_value': spec.min_value,
                    'typical_value': spec.typical_value,
                    'max_value': spec.max_value,
                    'unit': spec.unit,
                    'test_method': spec.test_method
                }
                spec_data.append(spec_dict)
            
            result = self.supabase.table('product_specifications').insert(spec_data).execute()
            return len(spec_data)
            
        except Exception as e:
            logger.error(f"Error saving specifications: {e}")
            logger.info("product_specifications table may not exist yet. Run TRL migration first.")
            return 0
    
    def process_products_for_specifications(self):
        """Main process to add specifications to products"""
        logger.info("Starting specification extraction process...")
        
        products = self.load_products_without_specs()
        
        if not products:
            logger.info("No products without specifications found")
            return
        
        total_specs_added = 0
        
        for i, product in enumerate(products):
            logger.info(f"Processing product {i+1}/{len(products)}: {product['name']}")
            
            all_specs = []
            
            # Extract specs from description
            if product.get('description'):
                desc_specs = self.extract_specs_from_description(
                    product['description'], 
                    product['plant_part_name']
                )
                for spec in desc_specs:
                    spec.product_id = product['id']
                all_specs.extend(desc_specs)
            
            # Add standard specs for plant part
            plant_part_specs = self.add_standard_specs_by_plant_part(
                product['id'], 
                product['plant_part_name']
            )
            all_specs.extend(plant_part_specs)
            
            # Save specifications
            if all_specs:
                saved_count = self.save_specifications(all_specs)
                total_specs_added += saved_count
                
                if saved_count > 0:
                    logger.info(f"  Added {saved_count} specifications")
                else:
                    logger.warning(f"  Failed to save specifications")
        
        logger.info(f"\n{'='*50}")
        logger.info("SPECIFICATION PROCESS COMPLETE")
        logger.info(f"Products processed: {len(products)}")
        logger.info(f"Total specifications added: {total_specs_added}")
        logger.info(f"Average specs per product: {total_specs_added/len(products):.1f}")
        logger.info("="*50)

def main():
    """Main entry point"""
    extractor = MaterialSpecificationExtractor()
    extractor.process_products_for_specifications()

if __name__ == "__main__":
    main()